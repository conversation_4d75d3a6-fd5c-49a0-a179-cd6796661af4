package com.airdoc.ytrts.home.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: Evaluation
 * Author by lilin,Date on 2025/6/17 16:30
 * PS: Not easy to write code, please indicate.
 * 评估报告
 */
@Parcelize
data class Evaluation(
    //报告ID
    var id: Long? = null,
    //患者姓名
    var name:String? = null,
    //患者性别{1=男, 2=女},可用值:1,2
    var gender:Int? = null,
    //年龄
    var age:Int? = null,
    //评估日期
    var createTime:String? = null,
    //患者ID
    var patientId: Long? = null,
    //评估报告结果
    var reportUrl: String? = null
): Parcelable
