package com.airdoc.ytrts.home.emr

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.ytrts.R
import com.airdoc.ytrts.databinding.ItemPatientHeaderLayoutBinding
import com.airdoc.ytrts.databinding.ItemPatientLayoutBinding
import com.airdoc.ytrts.home.bean.Patient
import com.airdoc.ytrts.user.enumeration.Gender

/**
 * FileName: PatientAdapter
 * Author by lilin,Date on 2025/6/16 17:34
 * PS: Not easy to write code, please indicate.
 */
class PatientAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val TYPE_HEADER = 0
        private const val TYPE_ITEM = 1
        //评估
        const val ACTION_EVALUATE = "evaluate"
        //修改
        const val ACTION_EDIT = "edit"
        //删除
        const val ACTION_DELETE = "delete"
    }

    private val patients = mutableListOf<Patient>()
    var onActionClick: ((action: String, patient: Patient) -> Unit)? = null

    fun submitList(newList: List<Patient>) {
        patients.clear()
        patients.addAll(newList)
        notifyDataSetChanged()
    }

    fun addMoreItems(newItems: List<Patient>) {
        val startPos = patients.size + 1
        patients.addAll(newItems)
        notifyItemRangeInserted(startPos, newItems.size)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == TYPE_HEADER) {
            HeaderHolder(
                ItemPatientHeaderLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            )
        } else {
            PatientHolder(
                ItemPatientLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            )
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is HeaderHolder -> holder.bind()
            is PatientHolder -> {
                if (position - 1 in patients.indices){
                    holder.bind(patients[position - 1])
                }
            }
        }
    }

    override fun getItemCount(): Int {
        return patients.size + 1
    }

    override fun getItemViewType(position: Int): Int {
        return if (position == 0) TYPE_HEADER else TYPE_ITEM
    }

    inner class HeaderHolder(val binding: ItemPatientHeaderLayoutBinding) : RecyclerView.ViewHolder(binding.root){

        fun bind(){
            binding.apply {
                root.setBackgroundColor("#EFF3F6".toColorInt())
                tvEmrId.textSize = 14f
                tvName.textSize = 14f
                tvGender.textSize = 14f
                tvAge.textSize = 14f
                tvPhone.textSize = 14f
                tvCreationDate.textSize = 14f
                tvRevisionDate.textSize = 14f
                tvOperate.textSize = 14f
                tvEmrId.text = root.context.getString(R.string.str_emr_id)
                tvName.text = root.context.getString(R.string.str_name)
                tvGender.text = root.context.getString(R.string.str_gender)
                tvAge.text = root.context.getString(R.string.str_age)
                tvPhone.text = root.context.getString(R.string.str_phone)
                tvCreationDate.text = root.context.getString(R.string.str_creation_date)
                tvRevisionDate.text = root.context.getString(R.string.str_revision_date)
                tvOperate.text = root.context.getString(R.string.str_operate)
            }
        }
    }

    inner class PatientHolder(val binding: ItemPatientLayoutBinding) : RecyclerView.ViewHolder(binding.root){

        fun bind(patient: Patient){
            binding.apply {
                root.background = null
                tvEmrId.textSize = 14f
                tvName.textSize = 14f
                tvGender.textSize = 14f
                tvAge.textSize = 14f
                tvPhone.textSize = 14f
                tvCreationDate.textSize = 14f
                tvRevisionDate.textSize = 14f
                tvEvaluation.textSize = 14f
                tvRevision.textSize = 14f
                tvDelete.textSize = 14f
                tvEmrId.text = patient.id.toString()
                tvName.text = patient.name
                tvGender.text = when(patient.gender){
                    Gender.FEMALE.num -> {
                        root.context.getString(R.string.str_female)
                    }
                    else -> {
                        root.context.getString(R.string.str_male)
                    }
                }
                tvAge.text = patient.age.toString()
                tvPhone.text = patient.phoneNumber
                tvCreationDate.text = patient.createTime
                tvRevisionDate.text = patient.updateTime
                tvEvaluation.setOnSingleClickListener {
                    onActionClick?.invoke(ACTION_EVALUATE,patient)
                }
                tvRevision.setOnSingleClickListener {
                    onActionClick?.invoke(ACTION_EDIT,patient)
                }
                tvDelete.setOnSingleClickListener {
                    onActionClick?.invoke(ACTION_DELETE,patient)
                }
            }
        }
    }

}