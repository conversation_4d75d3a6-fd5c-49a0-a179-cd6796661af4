package com.airdoc.ytrts

import android.content.Context
import java.util.Locale

/**
 * FileName: DeviceManager
 * Author by lilin,Date on 2025/6/19 19:08
 * PS: Not easy to write code, please indicate.
 */
object DeviceManager {

    fun getProductModel():String{
        return "YT_RTS"
    }

    /**
     * 获取设备当前语言
     */
    fun getLanguage(context: Context):Locale{
        val locales = context.resources.configuration.locales
        val locale = locales.get(0)
        return locale
    }

}