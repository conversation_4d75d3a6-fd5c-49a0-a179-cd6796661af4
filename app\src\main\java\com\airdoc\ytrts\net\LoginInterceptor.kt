package com.airdoc.ytrts.net

import android.os.Handler
import android.os.Looper
import com.airdoc.component.common.base.BaseCommonApplication
import com.airdoc.component.common.log.Logger
import com.airdoc.ytrts.user.LoginManager
import okhttp3.Interceptor
import okhttp3.Response

/**
 * FileName: LoginInterceptor
 * Author by lilin,Date on 2025/6/26 16:40
 * PS: Not easy to write code, please indicate.
 */
class LoginInterceptor : Interceptor {

    private val TAG = LoginInterceptor::class.java.simpleName

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val response = chain.proceed(request)
        Logger.d(TAG, msg = "LoginInterceptor intercept: code = ${response.code}")
        if (response.code == 401) {
            // 登录失效，跳转到登录页面
            // 登录成功，重新请求
            Handler(Looper.getMainLooper()).post {
                LoginManager.login(BaseCommonApplication.instance)
            }
        }
        return response
    }
}