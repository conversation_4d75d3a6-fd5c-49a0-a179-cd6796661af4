<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="400dp"
    android:layout_height="330dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/common_white_round_25_bg">

    <DatePicker
        android:id="@+id/date_picker"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:datePickerMode="spinner"
        android:calendarViewShown="false"
        android:spinnersShown="true"
        android:theme="@style/DatePickerTheme"
        android:layout_marginTop="40dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_cancel"
        android:textColor="#8D9EAC"
        android:textSize="17sp"
        android:gravity="center"
        android:background="@drawable/common_d6dce1_round_bg"
        android:focusable="true"
        android:layout_marginBottom="30dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_ok"
        app:layout_constraintHorizontal_chainStyle="packed"/>

    <TextView
        android:id="@+id/tv_ok"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_ok"
        android:textColor="@color/white"
        android:textSize="17sp"
        android:gravity="center"
        android:background="@drawable/common_eb4e89_round_bg"
        android:focusable="true"
        android:layout_marginBottom="30dp"
        android:layout_marginStart="15dp"
        app:layout_constraintLeft_toRightOf="@+id/tv_cancel"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>