package com.airdoc.ytrts.home.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.airdoc.ytrts.home.api.EvaluationApiService
import com.airdoc.ytrts.home.bean.EvaluationList
import com.airdoc.ytrts.home.bean.EvaluationReportResult
import com.airdoc.ytrts.net.MainRetrofitClient
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * FileName: EvaluationRepository
 * Author by lilin,Date on 2025/6/20 11:49
 * PS: Not easy to write code, please indicate.
 */
class EvaluationRepository : BaseRepository() {

    /**
     * 获取评估列表
     * @param page 页码,从1开始
     * @param size 每页条数,示例值(10)
     * @param sort 排序条件,示例值(按创建时间倒序：createTime,desc)
     * @param gender 性别{0=未知, 1=男, 2=女},可用值:0,1,2,示例值(1)
     * @param keywords 搜索关键词,示例值(张三)
     * @param authorization 授权token,示例值(Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c)
     */
    suspend fun getEvaluationList(page:Int,size:Int,sort:String?,
                               gender:Int?,keywords:String?,
                               authorization: String
    ): ApiResponse<EvaluationList> {
        return executeHttp {
            MainRetrofitClient.createService(EvaluationApiService::class.java).getEvaluationList(page, size, sort, gender, keywords,authorization)
        }
    }

    /**
     * 删除评估
     * @param ids 评估ID列表,示例值[1,2]
     * @param authorization 授权token,示例值(Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQ)
     */
    suspend fun deleteEvaluation(ids: List<Long>, authorization: String): ApiResponse<Any> {
        return executeHttp {
            val hashMap = HashMap<String, Any>()
            hashMap["ids"] = ids
            val json = gson.toJson(hashMap)
            val requestBody =
                json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
            MainRetrofitClient.createService(EvaluationApiService::class.java).deleteEvaluation(requestBody,authorization)
        }
    }

    /**
     * 提交评估数据
     * @param result 评估结果,json 格式
     * @param authorization 授权token,示例值(Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQ)
     */
    suspend fun reportEvaluationData(result: String, authorization: String): ApiResponse<EvaluationReportResult> {
        return executeHttp {
            val requestBody =
                result.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
            MainRetrofitClient.createService(EvaluationApiService::class.java).reportEvaluationData(requestBody,authorization)
        }
    }

}