package com.airdoc.ytrts.user.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: LoginInfo
 * Author by lilin,Date on 2025/6/19 17:41
 * PS: Not easy to write code, please indicate.
 */
@Parcelize
data class LoginInfo(
    // 登录令牌
    var token: String? = null,
    // 令牌类型
    var tokenType: String? = null,
    // 令牌名称
    var tokenName: String? = null,
): Parcelable
