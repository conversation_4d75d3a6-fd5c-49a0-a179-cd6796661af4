<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .code { font-family: 'Courier New', monospace; font-size: 10px; fill: #e74c3c; }
      .ppg-line { stroke: #3498db; stroke-width: 3; fill: none; }
      .peak-point { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .valley-point { fill: #27ae60; stroke: #229954; stroke-width: 2; }
      .grid-line { stroke: #ecf0f1; stroke-width: 1; }
      .axis-line { stroke: #34495e; stroke-width: 2; }
      .threshold-line { stroke: #f39c12; stroke-width: 2; stroke-dasharray: 5,5; }
      .detection-box { fill: #f8f9fa; stroke: #dee2e6; stroke-width: 1; rx: 5; }
      .peak-box { fill: #fdf2f2; stroke: #e74c3c; stroke-width: 2; rx: 5; }
      .valley-box { fill: #f0f9f0; stroke: #27ae60; stroke-width: 2; rx: 5; }
    </style>
  </defs>

  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" class="title">PPG信号峰值与谷底检测算法详解</text>

  <!-- 主要PPG波形图 -->
  <rect x="50" y="60" width="700" height="300" class="detection-box"/>
  <text x="400" y="80" text-anchor="middle" class="subtitle">PPG信号波形与峰谷检测</text>
  
  <g transform="translate(70, 100)">
    <!-- 网格线 -->
    <line x1="0" y1="0" x2="660" y2="0" class="grid-line"/>
    <line x1="0" y1="60" x2="660" y2="60" class="grid-line"/>
    <line x1="0" y1="120" x2="660" y2="120" class="grid-line"/>
    <line x1="0" y1="180" x2="660" y2="180" class="grid-line"/>
    <line x1="0" y1="240" x2="660" y2="240" class="grid-line"/>
    
    <!-- 坐标轴 -->
    <line x1="0" y1="0" x2="0" y2="240" class="axis-line"/>
    <line x1="0" y1="240" x2="660" y2="240" class="axis-line"/>
    
    <!-- 阈值线 -->
    <line x1="0" y1="60" x2="660" y2="60" class="threshold-line"/>
    <text x="670" y="65" class="small-text">峰值阈值</text>
    
    <line x1="0" y1="180" x2="660" y2="180" class="threshold-line"/>
    <text x="670" y="185" class="small-text">谷底阈值</text>
    
    <!-- PPG波形 - 模拟真实心跳波形 -->
    <path d="M0,120 L40,115 Q60,40 80,120 L120,125 Q140,35 160,120 L200,130 Q220,30 240,120 L280,135 Q300,25 320,120 L360,140 Q380,20 400,120 L440,145 Q460,15 480,120 L520,150 Q540,10 560,120 L600,155 Q620,5 640,120 L660,160" class="ppg-line"/>
    
    <!-- 峰值标记 -->
    <circle cx="60" cy="40" r="5" class="peak-point"/>
    <circle cx="140" cy="35" r="5" class="peak-point"/>
    <circle cx="220" cy="30" r="5" class="peak-point"/>
    <circle cx="300" cy="25" r="5" class="peak-point"/>
    <circle cx="380" cy="20" r="5" class="peak-point"/>
    <circle cx="460" cy="15" r="5" class="peak-point"/>
    <circle cx="540" cy="10" r="5" class="peak-point"/>
    <circle cx="620" cy="5" r="5" class="peak-point"/>
    
    <!-- 谷底标记 -->
    <circle cx="100" cy="125" r="4" class="valley-point"/>
    <circle cx="180" cy="130" r="4" class="valley-point"/>
    <circle cx="260" cy="135" r="4" class="valley-point"/>
    <circle cx="340" cy="140" r="4" class="valley-point"/>
    <circle cx="420" cy="145" r="4" class="valley-point"/>
    <circle cx="500" cy="150" r="4" class="valley-point"/>
    <circle cx="580" cy="155" r="4" class="valley-point"/>
    
    <!-- 数据点标注 -->
    <text x="60" y="30" text-anchor="middle" class="small-text">R1</text>
    <text x="140" y="25" text-anchor="middle" class="small-text">R2</text>
    <text x="220" y="20" text-anchor="middle" class="small-text">R3</text>
    <text x="300" y="15" text-anchor="middle" class="small-text">R4</text>
    
    <text x="100" y="140" text-anchor="middle" class="small-text">V1</text>
    <text x="180" y="145" text-anchor="middle" class="small-text">V2</text>
    <text x="260" y="150" text-anchor="middle" class="small-text">V3</text>
    
    <!-- 原始数据示例 -->
    <text x="0" y="260" class="small-text">waveIntData示例: [56, 55, 53, 52, 50, 48, 45, 42, 38, 35, 40, 45, 52, 58, 62, 65, 68, 70, 68, 65, 60, 55, 50, ...]</text>
    <text x="0" y="275" class="small-text">这些都是连续的PPG信号采样值，不是峰值。峰值和谷底需要通过算法检测。</text>
    
    <!-- 时间轴标注 -->
    <text x="0" y="255" text-anchor="middle" class="small-text">0ms</text>
    <text x="165" y="255" text-anchor="middle" class="small-text">1000ms</text>
    <text x="330" y="255" text-anchor="middle" class="small-text">2000ms</text>
    <text x="495" y="255" text-anchor="middle" class="small-text">3000ms</text>
    <text x="660" y="255" text-anchor="middle" class="small-text">4000ms</text>
    
    <!-- 幅值标注 -->
    <text x="-20" y="5" text-anchor="middle" class="small-text">100</text>
    <text x="-20" y="65" text-anchor="middle" class="small-text">80</text>
    <text x="-20" y="125" text-anchor="middle" class="small-text">60</text>
    <text x="-20" y="185" text-anchor="middle" class="small-text">40</text>
    <text x="-20" y="245" text-anchor="middle" class="small-text">20</text>
  </g>

  <!-- 峰值检测算法 -->
  <rect x="800" y="60" width="350" height="140" class="peak-box"/>
  <text x="975" y="80" text-anchor="middle" class="subtitle">峰值检测算法</text>
  
  <g transform="translate(820, 90)">
    <text x="0" y="20" class="text">1. 阈值检测:</text>
    <text x="0" y="35" class="code">if (data[i] > threshold &&</text>
    <text x="0" y="50" class="code">    data[i] > data[i-1] && data[i] > data[i+1])</text>
    
    <text x="0" y="75" class="text">2. 导数零点检测:</text>
    <text x="0" y="90" class="code">if (derivatives[i-1] > 0 && derivatives[i] <= 0)</text>
    
    <text x="0" y="115" class="text">3. 局部最大值验证:</text>
    <text x="0" y="130" class="code">data[peak] > data[peak-1] && data[peak] > data[peak+1]</text>
  </g>

  <!-- 谷底检测算法 -->
  <rect x="800" y="220" width="350" height="140" class="valley-box"/>
  <text x="975" y="240" text-anchor="middle" class="subtitle">谷底检测算法</text>
  
  <g transform="translate(820, 250)">
    <text x="0" y="20" class="text">1. 阈值检测:</text>
    <text x="0" y="35" class="code">if (data[i] < threshold &&</text>
    <text x="0" y="50" class="code">    data[i] < data[i-1] && data[i] < data[i+1])</text>
    
    <text x="0" y="75" class="text">2. 导数零点检测:</text>
    <text x="0" y="90" class="code">if (derivatives[i-1] < 0 && derivatives[i] >= 0)</text>
    
    <text x="0" y="115" class="text">3. 局部最小值验证:</text>
    <text x="0" y="130" class="code">data[valley] < data[valley-1] && data[valley] < data[valley+1]</text>
  </g>

  <!-- 检测结果对比 -->
  <rect x="50" y="400" width="1100" height="180" class="detection-box"/>
  <text x="600" y="420" text-anchor="middle" class="subtitle">峰值与谷底检测结果对比</text>
  
  <g transform="translate(70, 440)">
    <!-- 峰值检测结果 -->
    <text x="0" y="20" class="text">峰值检测结果:</text>
    <rect x="150" y="5" width="400" height="25" fill="#fdf2f2" stroke="#e74c3c" stroke-width="1" rx="3"/>
    <text x="160" y="22" class="code">peaks = [60, 140, 220, 300, 380, 460, 540, 620]</text>
    <text x="560" y="22" class="small-text">← 数组索引位置</text>
    
    <text x="0" y="50" class="text">对应PPG值:</text>
    <rect x="150" y="35" width="400" height="25" fill="#fdf2f2" stroke="#e74c3c" stroke-width="1" rx="3"/>
    <text x="160" y="52" class="code">values = [68, 70, 72, 75, 78, 82, 85, 88]</text>
    <text x="560" y="52" class="small-text">← 实际信号幅值</text>
    
    <!-- 谷底检测结果 -->
    <text x="0" y="80" class="text">谷底检测结果:</text>
    <rect x="150" y="65" width="400" height="25" fill="#f0f9f0" stroke="#27ae60" stroke-width="1" rx="3"/>
    <text x="160" y="82" class="code">valleys = [100, 180, 260, 340, 420, 500, 580]</text>
    <text x="560" y="82" class="small-text">← 数组索引位置</text>
    
    <text x="0" y="110" class="text">对应PPG值:</text>
    <rect x="150" y="95" width="400" height="25" fill="#f0f9f0" stroke="#27ae60" stroke-width="1" rx="3"/>
    <text x="160" y="112" class="code">values = [45, 42, 38, 35, 32, 30, 28]</text>
    <text x="560" y="112" class="small-text">← 实际信号幅值</text>
    
    <!-- 应用场景 -->
    <text x="0" y="140" class="text">应用场景:</text>
    <text x="150" y="140" class="text">• 峰值(R波): 用于计算心率和RR间期，HRV分析的核心</text>
    <text x="150" y="155" class="text">• 谷底: 用于信号质量评估、基线校正、波形完整性检查</text>
  </g>

  <!-- 技术要点说明 -->
  <rect x="50" y="600" width="1100" height="80" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1" rx="3"/>
  <text x="600" y="620" text-anchor="middle" class="subtitle">关键技术要点</text>
  
  <g transform="translate(70, 635)">
    <text x="0" y="15" class="small-text">1. waveIntData是原始连续采样数据，每20ms一个数据点，不是峰值数据</text>
    <text x="0" y="30" class="small-text">2. 峰值检测使用多策略算法：阈值+导数+局部最大值验证，确保检测准确性</text>
    <text x="0" y="45" class="small-text">3. 谷底检测原理相同但条件相反：寻找局部最小值点，用于信号质量评估</text>
    <text x="0" y="60" class="small-text">4. HRV分析主要使用峰值(R波)计算RR间期，谷底主要用于辅助信号处理</text>
  </g>

</svg>
