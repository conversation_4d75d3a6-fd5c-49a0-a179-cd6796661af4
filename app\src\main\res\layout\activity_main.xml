<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".home.MainActivity"
    android:background="@drawable/app_launcher_bg">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_setting"
        android:layout_width="40dp"
        android:layout_height="40dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="25dp"
        android:visibility="gone">

        <ImageView
            android:id="@+id/iv_setting"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/ic_menu" />

        <ImageView
            android:id="@+id/iv_setting_red_dot"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:src="@drawable/ic_red_dot"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:visibility="gone"
            tools:visibility="visible"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/ll_user_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="25dp">

        <ImageView
            android:id="@+id/iv_user_avatar"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:src="@drawable/ic_male_avatar_round" />

        <TextView
            android:id="@+id/tv_user_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="张三"
            android:textColor="@color/color_333333"
            android:textSize="16sp"
            android:textStyle="bold"
            android:includeFontPadding="false" />

    </LinearLayout>

    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:src="@drawable/ic_main_logo"
        android:scaleType="fitStart"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ll_user_info"
        app:layout_constraintBottom_toBottomOf="@+id/ll_user_info"
        android:layout_marginStart="20dp"/>

    <TextView
        android:id="@+id/tv_evaluation_management"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:minWidth="88dp"
        android:background="@drawable/selector_home_function_bg"
        android:text="@string/str_evaluation_management"
        android:textColor="@color/selector_home_function_text_color"
        android:textSize="16sp"
        android:gravity="center"
        android:paddingStart="15dp"
        android:paddingEnd="15dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintTop_toTopOf="@+id/ll_user_info"
        app:layout_constraintBottom_toBottomOf="@+id/ll_user_info"
        app:layout_constraintRight_toLeftOf="@+id/ll_user_info"/>

    <TextView
        android:id="@+id/tv_emr_management"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:minWidth="88dp"
        android:background="@drawable/selector_home_function_bg"
        android:text="@string/str_emr_management"
        android:textColor="@color/selector_home_function_text_color"
        android:textSize="16sp"
        android:gravity="center"
        android:paddingStart="15dp"
        android:paddingEnd="15dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintTop_toTopOf="@+id/ll_user_info"
        app:layout_constraintBottom_toBottomOf="@+id/ll_user_info"
        app:layout_constraintRight_toLeftOf="@+id/tv_evaluation_management"/>

    <FrameLayout
        android:id="@+id/fl_content_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="85dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>