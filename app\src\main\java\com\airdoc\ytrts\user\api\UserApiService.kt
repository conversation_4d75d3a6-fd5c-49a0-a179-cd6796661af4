package com.airdoc.ytrts.user.api

import com.airdoc.component.common.net.entity.ApiResponse
import com.airdoc.ytrts.user.bean.LoginInfo
import com.airdoc.ytrts.user.bean.User
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.PATCH
import retrofit2.http.POST

/**
 * FileName: UserApiService
 * Author by lilin,Date on 2025/6/19 17:34
 * PS: Not easy to write code, please indicate.
 */
interface UserApiService {

    /**
     * 获取用户信息
     */
    @GET("re-mpd/api/account/info")
    suspend fun getUserInfo(
        @Header("Authorization") authorization: String
    ): ApiResponse<User>

    /**
     * 登录
     */
    @POST("re-mpd/api/account/login")
    suspend fun login(
        @Body patientReq: RequestBody
    ): ApiResponse<LoginInfo>

    /**
     * 等出
     */
    @POST("re-mpd/api/account/logout")
    suspend fun logout(
        @Header("Authorization") authorization: String
    ): ApiResponse<Any>

    /**
     * 修改用户信息
     */
    @PATCH("re-mpd/api/account/basic/info")
    suspend fun updateUserInfo(
        @Header("Authorization") authorization: String,
        @Body patientReq: RequestBody
    ): ApiResponse<Any>

    /**
     * 修改密码
     */
    @PATCH("re-mpd/api/account/password")
    suspend fun changePassword(
        @Header("Authorization") authorization: String,
        @Body patientReq: RequestBody
    ): ApiResponse<Any>

}