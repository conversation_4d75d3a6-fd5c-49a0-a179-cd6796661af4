package com.airdoc.ytrts.user

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.widget.Toast
import androidx.activity.viewModels
import com.airdoc.component.common.base.BaseCommonActivity
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.ytrts.R
import com.airdoc.ytrts.common.RSAManager
import com.airdoc.ytrts.databinding.ActivityChangePasswordBinding
import com.airdoc.ytrts.common.hasDigit
import com.airdoc.ytrts.common.hasLetter
import com.airdoc.ytrts.user.vm.UserViewModel
import kotlin.getValue

/**
 * FileName: ChangePasswordActivity
 * Author by lilin,Date on 2025/6/19 15:12
 * PS: Not easy to write code, please indicate.
 * 修改密码页面
 */
class ChangePasswordActivity : BaseCommonActivity() {

    companion object{
        private val TAG = ChangePasswordActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            val intent = Intent(context, ChangePasswordActivity::class.java)
            return intent
        }
    }

    private lateinit var binding: ActivityChangePasswordBinding
    private val userVM by viewModels<UserViewModel>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityChangePasswordBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initView()
        initObserver()
    }

    private fun initView(){
        initListener()
    }

    private fun initObserver() {
        userVM.changePasswordLiveData.observe(this){
            when (it) {
                is String -> {
                    Toast.makeText(this,getString(R.string.str_password_modified_successfully),Toast.LENGTH_SHORT).show()
                    val returnIntent = Intent()
                    setResult(RESULT_OK, returnIntent)
                    finish()
                }
                is Pair<*,*> -> {
                    Toast.makeText(this,"${it.second}",Toast.LENGTH_SHORT).show()
                }
                else -> {
                    Toast.makeText(this,getString(R.string.str_password_modified_failed),Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun initListener(){
        binding.ivBack.setOnSingleClickListener {
            finish()
        }
        binding.tvCancel.setOnSingleClickListener {
            finish()
        }
        binding.tvOk.setOnSingleClickListener {
            val originalPassword = binding.etOriginalPassword.text.toString()
            val newPassword = binding.etNewPassword.text.toString()
            val confirmPassword = binding.etConfirmPassword.text.toString()
            if (checkPassword(originalPassword, newPassword, confirmPassword)){
                userVM.changePassword(RSAManager.rsaEncryptWithPublicKey(originalPassword, UserManager.PASSWORD_PUBLIC_KEY),
                    RSAManager.rsaEncryptWithPublicKey(newPassword, UserManager.PASSWORD_PUBLIC_KEY))
            }
        }

        binding.ivDisplayPassword.setOnSingleClickListener {
            binding.ivDisplayPassword.isSelected = !binding.ivDisplayPassword.isSelected
            if (binding.ivDisplayPassword.isSelected){
                binding.etOriginalPassword.transformationMethod = HideReturnsTransformationMethod.getInstance()
                binding.etNewPassword.transformationMethod = HideReturnsTransformationMethod.getInstance()
                binding.etConfirmPassword.transformationMethod = HideReturnsTransformationMethod.getInstance()
            }else{
                binding.etOriginalPassword.transformationMethod = PasswordTransformationMethod.getInstance()
                binding.etNewPassword.transformationMethod = PasswordTransformationMethod.getInstance()
                binding.etConfirmPassword.transformationMethod = PasswordTransformationMethod.getInstance()
            }

            binding.etOriginalPassword.setSelection(binding.etOriginalPassword.text.length)
            binding.etNewPassword.setSelection(binding.etNewPassword.text.length)
            binding.etConfirmPassword.setSelection(binding.etConfirmPassword.text.length)
        }
    }

    private fun checkPassword(originalPassword: String,newPassword: String,confirmPassword: String): Boolean{
        if (originalPassword.isBlank()){
            Toast.makeText(this, getString(R.string.str_original_password_empty), Toast.LENGTH_SHORT).show()
            return false
        }
        if (newPassword != confirmPassword){
            Toast.makeText(this, getString(R.string.str_new_password_confirmation_differ), Toast.LENGTH_SHORT).show()
            return false
        }

        // 使用新的密码验证方法
        val passwordError = UserManager.validatePassword(newPassword)
        if (passwordError != null) {
            Toast.makeText(this, passwordError, Toast.LENGTH_SHORT).show()
            return false
        }

        return true
    }

}