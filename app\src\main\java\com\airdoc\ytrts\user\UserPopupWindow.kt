package com.airdoc.ytrts.user

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.PopupWindow
import com.airdoc.ytrts.databinding.PopupWindowUserBinding
import androidx.core.graphics.drawable.toDrawable
import com.airdoc.component.common.ktx.setOnSingleClickListener

/**
 * FileName: UserPopupWindow
 * Author by lilin,Date on 2025/6/19 10:19
 * PS: Not easy to write code, please indicate.
 */
class UserPopupWindow(val context: Context) : PopupWindow() {

    companion object{
        private val TAG = UserPopupWindow::class.java.simpleName
        //评估
        const val ACTION_USER_INFO = "UserInfo"
        //修改
        const val ACTION_CHANGE_PASSWORD = "ChangePassword"
        //删除
        const val ACTION_LOGOUT = "LogOut"
    }
    private var binding: PopupWindowUserBinding = PopupWindowUserBinding.inflate(LayoutInflater.from(context))
    var onActionClick: ((action: String) -> Unit)? = null

    init {
        contentView = binding.root

        isFocusable = true
        isTouchable = true
        isOutsideTouchable = true

        initView()

        initListener()

    }

    private fun initView() {
        width = ViewGroup.LayoutParams.WRAP_CONTENT
        height = ViewGroup.LayoutParams.WRAP_CONTENT

        setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
    }

    private fun initListener() {
        binding.llUserInfo.setOnSingleClickListener {
            onActionClick?.invoke(ACTION_USER_INFO)
            dismiss()
        }
        binding.llChangePassword.setOnSingleClickListener {
            onActionClick?.invoke(ACTION_CHANGE_PASSWORD)
            dismiss()
        }
        binding.llLogOut.setOnSingleClickListener {
            onActionClick?.invoke(ACTION_LOGOUT)
            dismiss()
        }
    }

}