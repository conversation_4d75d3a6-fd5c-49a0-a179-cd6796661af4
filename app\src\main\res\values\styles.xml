<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="DatePickerTheme" parent="Theme.AppCompat.Light">
        <!-- 分割线颜色 -->
        <item name="colorControlNormal">@color/color_CDCDCD</item>

        <!-- 文本颜色 -->
        <item name="android:textColor">@color/color_333333</item>

        <!-- 可选：设置文本大小 -->
        <item name="android:textSize">16sp</item>
    </style>

    <style name="common_card" parent="CardView">
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">5dp</item>
    </style>

</resources>