package com.airdoc.ytrts.home.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.airdoc.ytrts.home.bean.EvaluationList
import com.airdoc.ytrts.home.bean.EvaluationReportResult
import com.airdoc.ytrts.home.repository.EvaluationRepository
import com.airdoc.ytrts.user.UserManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import kotlin.random.Random

/**
 * FileName: EvaluationViewModel
 * Author by lilin,Date on 2025/6/17 17:13
 * PS: Not easy to write code, please indicate.
 */
class EvaluationViewModel : ViewModel() {

    companion object{
        private val TAG = EvaluationViewModel::class.java.name
    }

    private val evaluationRepository by lazy { EvaluationRepository() }
    //评估列表
    val evaluationListLiveData = MutableLiveData<EvaluationList?>()
    //删除评估
    val deleteEvaluationLiveData = MutableLiveData<Boolean>()
    //提交评估数据
    val reportEvaluationLiveData = MutableLiveData<EvaluationReportResult?>()

    /**
     * 获取患者列表
     * @param page 页码,从1开始
     * @param size 每页条数,示例值(10)
     * @param sort 排序条件,示例值(按创建时间倒序：createTime,desc)
     * @param gender 性别{1=男, 2=女},可用值:0,1,2,示例值(1)
     * @param keywords 搜索关键词,示例值(张三)
     */
    fun getEvaluationList(page:Int,size:Int = 10,sort:String? = null,gender:Int? = null,keywords:String? = null){
        viewModelScope.launch {
            MutableStateFlow(evaluationRepository.getEvaluationList(page, size,
                sort ?: "createTime,desc", gender, keywords?.ifEmpty { null }, UserManager.getAuthorization())).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "getPatientList onSuccess")
                    evaluationListLiveData.postValue(it)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "getPatientList onDataEmpty")
                    evaluationListLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "getPatientList onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    evaluationListLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "getPatientList onError = $it")
                    evaluationListLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 删除评估
     * @param ids 评估ID列表,示例值[1,2]
     */
    fun deleteEvaluation(ids: List<Long>){
        viewModelScope.launch {
            MutableStateFlow(evaluationRepository.deleteEvaluation(ids, UserManager.getAuthorization())).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "deletePatient onSuccess")
                    deleteEvaluationLiveData.postValue(true)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "deletePatient onDataEmpty")
                    deleteEvaluationLiveData.postValue(true)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "deletePatient onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    deleteEvaluationLiveData.postValue(false)
                }
                onError = {
                    Logger.e(TAG, msg = "deletePatient onError = $it")
                    deleteEvaluationLiveData.postValue(false)
                }
            }
        }
    }

    /**
     * 提交评估数据
     * @param result 评估结果,json 格式
     */
    fun reportEvaluationData(result: String){
        viewModelScope.launch {
            MutableStateFlow(evaluationRepository.reportEvaluationData(result, UserManager.getAuthorization())).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "reportEvaluationData onSuccess")
                    reportEvaluationLiveData.postValue(it)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "reportEvaluationData onDataEmpty")
                    reportEvaluationLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "reportEvaluationData onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    reportEvaluationLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "reportEvaluationData onError = $it")
                    reportEvaluationLiveData.postValue(null)
                }
            }
        }
    }

    fun generateRandomDateTime(): String {
        // 定义 2025 年的起始和结束时间
        val start = LocalDateTime.of(2025, 1, 1, 0, 0, 0)
        val end = LocalDateTime.of(2025, 12, 31, 23, 59, 59)

        // 计算总秒数范围
        val startEpoch = start.toEpochSecond(ZoneOffset.UTC)
        val endEpoch = end.toEpochSecond(ZoneOffset.UTC)

        // 生成随机秒数
        val randomSeconds = Random.nextLong(startEpoch, endEpoch + 1)

        // 将随机秒数转换为 LocalDateTime
        val randomDateTime = LocalDateTime.ofEpochSecond(randomSeconds, 0, ZoneOffset.UTC)

        // 格式化为 "yyyy-MM-dd HH:mm:ss"
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        return randomDateTime.format(formatter)
    }

    fun generateRandomChineseString(lengthRange: IntRange = 2..8): String {
        // 定义 CJK 汉字的 Unicode 范围（U+4E00 ~ U+9FFF）
        val start = 0x4E00
        val end = 0x9FFF

        // 随机确定字符串长度（2~8）
        val length = Random.nextInt(lengthRange.first, lengthRange.last + 1)

        // 生成随机汉字字符串
        return (1..length).map {
            // 生成随机码点并转换为 Char
            (Random.nextInt(start, end + 1)).toChar()
        }.joinToString("")
    }

}