pluginManagement {
    repositories {
        // AliRepo 阿里仓库服务 https://maven.aliyun.com/mvn/view
        maven {
            url = uri("https://maven.aliyun.com/repository/public")
        }
        maven {
            url = uri("https://maven.aliyun.com/repository/google")
        }
        maven {
            url = uri("https://maven.aliyun.com/repository/gradle-plugin")
        }
        //LibCommon 公共库
        maven {
            url = uri("http://10.1.3.144:8081/repository/airdoc-snapshot/")
            isAllowInsecureProtocol = true
        }
        maven {
            url = uri("http://10.1.3.144:8081/repository/airdoc-release/")
            isAllowInsecureProtocol = true
        }
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        // AliRepo 阿里仓库服务 https://maven.aliyun.com/mvn/view
        maven {
            url = uri("https://maven.aliyun.com/repository/public")
        }
        maven {
            url = uri("https://maven.aliyun.com/repository/google")
        }
        maven {
            url = uri("https://maven.aliyun.com/repository/gradle-plugin")
        }
        //LibCommon 公共库
        maven {
            url = uri("http://10.1.3.144:8081/repository/airdoc-snapshot/")
            isAllowInsecureProtocol = true
        }
        maven {
            url = uri("http://10.1.3.144:8081/repository/airdoc-release/")
            isAllowInsecureProtocol = true
        }
        google()
        mavenCentral()
    }
}

rootProject.name = "YtRts"
include(":app")
 