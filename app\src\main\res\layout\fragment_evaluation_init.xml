<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv_wear"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_wear_device_properly"
        android:textColor="@color/color_333333"
        android:textSize="16sp"
        android:layout_marginTop="20dp"
        android:layout_marginStart="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_scan"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:minWidth="88dp"
        android:background="@drawable/common_eff3f6_round_20_bg"
        android:text="@string/str_scan"
        android:textColor="@color/color_333333"
        android:textSize="14sp"
        android:gravity="center"
        android:visibility="gone"
        android:paddingStart="15dp"
        android:paddingEnd="15dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintTop_toTopOf="@+id/tv_wear"
        app:layout_constraintBottom_toBottomOf="@+id/tv_wear"
        app:layout_constraintRight_toRightOf="parent"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_device"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_wear"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/tv_no_devices_available"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_no_devices_available_searching"
        android:textSize="18sp"
        android:textColor="@color/color_333333"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/rv_device"
        app:layout_constraintBottom_toBottomOf="@+id/rv_device"/>

</androidx.constraintlayout.widget.ConstraintLayout>