package com.airdoc.ytrts.net

import com.airdoc.component.common.base.BaseCommonApplication
import com.airdoc.component.common.utils.DeviceUtils
import com.airdoc.component.common.utils.PackageUtils
import com.airdoc.ytrts.DeviceManager
import okhttp3.Headers
import okhttp3.Interceptor
import okhttp3.Response

/**
 * FileName: CommonParamsInterceptor
 * Author by lilin,Date on 2025/6/19 19:06
 * PS: Not easy to write code, please indicate.
 */
class CommonParamsInterceptor : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        val oldRequest = chain.request()

        val builder = Headers.Builder()
        oldRequest.headers.forEach {
            builder.add(it.first, it.second)
        }
        builder["X-Device-Sn"] = DeviceUtils.getDeviceSerial()
        builder["X-App-Version"] = PackageUtils.getVersionName(
            BaseCommonApplication.instance,
            BaseCommonApplication.instance.packageName)
        builder["X-Device-Mode"] = DeviceManager.getProductModel()
        builder["X-Airdoc-Client"] = "e4d4b6c3-2b49-440e-98fd-0837a5fcb858"
        val locale = DeviceManager.getLanguage(BaseCommonApplication.instance)
        builder["Accept-Language"] = "${locale.language}-${locale.country}"

        val request = oldRequest.newBuilder().headers(builder.build()).build()
        return chain.proceed(request)
    }

}