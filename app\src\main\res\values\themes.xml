<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.YtRts" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>

    <style name="Theme.App" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="android:windowNoTitle">true</item>
    </style>

    <style name="LauncherActivityTheme" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowBackground">@drawable/launcher_splash_drawable</item>
        <item name="android:windowNoTitle">true</item>
    </style>

</resources>