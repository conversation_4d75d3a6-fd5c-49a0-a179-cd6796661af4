# Lepu设备集成快速启动模板

## 📁 项目结构建议

```
app/
├── src/main/java/com/yourpackage/
│   ├── lepu/
│   │   ├── manager/
│   │   │   ├── LepuDeviceManager.kt          # 设备管理核心类
│   │   │   ├── PPGDataProcessor.kt           # PPG数据处理
│   │   │   └── BleConnectionManager.kt       # 蓝牙连接管理
│   │   ├── model/
│   │   │   ├── PPGDataPoint.kt              # PPG数据点模型
│   │   │   ├── AnalysisResult.kt            # 分析结果模型
│   │   │   └── DeviceInfo.kt                # 设备信息模型
│   │   ├── ui/
│   │   │   ├── DeviceScanActivity.kt        # 设备扫描界面
│   │   │   ├── DataCollectionActivity.kt    # 数据采集界面
│   │   │   ├── PPGWaveView.kt               # 波形显示控件
│   │   │   └── DeviceListAdapter.kt         # 设备列表适配器
│   │   ├── viewmodel/
│   │   │   ├── DeviceViewModel.kt           # 设备状态管理
│   │   │   └── DataCollectionViewModel.kt   # 数据采集管理
│   │   └── utils/
│   │       ├── PPGManager.kt                # PPG分析工具类
│   │       ├── PermissionHelper.kt          # 权限管理工具
│   │       └── FileUtils.kt                 # 文件操作工具
│   └── libs/
│       └── lepu-blepro-1.0.8.aar           # Lepu SDK
```

## 🚀 核心类模板

### 1. LepuDeviceManager.kt - 设备管理核心

```kotlin
object LepuDeviceManager {
    
    private const val TAG = "LepuDeviceManager"
    private var isInitialized = false
    
    /**
     * 初始化Lepu服务
     */
    fun initialize(application: Application): Boolean {
        return try {
            if (!isInitialized) {
                val rawFolders = SparseArray<String>()
                rawFolders.set(Bluetooth.MODEL_PC60FW, 
                    "${application.getExternalFilesDir(null)?.absolutePath}/pc60fw")
                
                BleServiceHelper.BleServiceHelper
                    .initRawFolder(rawFolders)
                    .initService(application)
                    .initLog(false)
                
                isInitialized = true
                Log.d(TAG, "Lepu服务初始化成功")
            }
            true
        } catch (e: Exception) {
            Log.e(TAG, "Lepu服务初始化失败: ${e.message}")
            false
        }
    }
    
    /**
     * 开始扫描设备
     */
    fun startScan(models: IntArray = intArrayOf(Bluetooth.MODEL_PC60FW)) {
        BleServiceHelper.BleServiceHelper.startScan(models)
    }
    
    /**
     * 停止扫描
     */
    fun stopScan() {
        BleServiceHelper.BleServiceHelper.stopScan()
    }
    
    /**
     * 连接设备
     */
    fun connectDevice(context: Context, bluetooth: Bluetooth): Boolean {
        return try {
            BleServiceHelper.BleServiceHelper.setInterfaces(bluetooth.model)
            BleServiceHelper.BleServiceHelper.stopScan()
            BleServiceHelper.BleServiceHelper.connect(context, bluetooth.model, bluetooth.device)
            BluetoothController.clear()
            true
        } catch (e: Exception) {
            Log.e(TAG, "设备连接失败: ${e.message}")
            false
        }
    }
    
    /**
     * 断开设备
     */
    fun disconnectDevice() {
        BleServiceHelper.BleServiceHelper.disconnect(false)
    }
    
    /**
     * 获取已发现的设备列表
     */
    fun getDiscoveredDevices(): List<Bluetooth> {
        return BluetoothController.getDevices()
    }
}
```

### 2. PPGDataProcessor.kt - 数据处理器

```kotlin
class PPGDataProcessor {
    
    private val ppgDataPoints = mutableListOf<PPGDataPoint>()
    private val gson = Gson()
    
    /**
     * 处理实时波形数据
     */
    fun processWaveData(rtWave: RtWave) {
        val waveData = rtWave.waveIntData.toList()
        
        waveData.forEach { value ->
            val timestamp = if (ppgDataPoints.isEmpty()) {
                System.currentTimeMillis() * 1_000_000L + System.nanoTime() % 1_000_000L
            } else {
                ppgDataPoints.last().timestamp + 20 * 1_000_000L
            }
            
            ppgDataPoints.add(PPGDataPoint(value.toDouble(), timestamp))
        }
    }
    
    /**
     * 获取当前PPG数据
     */
    fun getCurrentData(): List<PPGDataPoint> {
        return ppgDataPoints.toList()
    }
    
    /**
     * 分析PPG数据
     */
    fun analyzeData(): AnalysisResult? {
        return if (ppgDataPoints.size >= 1000) { // 至少需要1000个数据点
            PPGManager.analyzeECG(ppgDataPoints)
        } else {
            null
        }
    }
    
    /**
     * 清空数据
     */
    fun clearData() {
        ppgDataPoints.clear()
    }
    
    /**
     * 保存数据到文件
     */
    fun saveDataToFile(context: Context, fileName: String): String? {
        val folder = PPGManager.createTimestampedFolder(context, "ppg_data")
        return folder?.let {
            PPGManager.createFormattedJsonFile(gson.toJson(ppgDataPoints), it, fileName)
        }
    }
}
```

### 3. DeviceViewModel.kt - 设备状态管理

```kotlin
class DeviceViewModel : ViewModel() {
    
    private val _discoveredDevices = MutableLiveData<List<Bluetooth>>()
    val discoveredDevices: LiveData<List<Bluetooth>> = _discoveredDevices
    
    private val _connectedDevice = MutableLiveData<Bluetooth?>()
    val connectedDevice: LiveData<Bluetooth?> = _connectedDevice
    
    private val _connectionState = MutableLiveData<Int>()
    val connectionState: LiveData<Int> = _connectionState
    
    private val _realTimeParams = MutableLiveData<RtParam>()
    val realTimeParams: LiveData<RtParam> = _realTimeParams
    
    init {
        initEventObservers()
    }
    
    private fun initEventObservers() {
        // 监听设备发现
        LiveEventBus.get<Bluetooth>(EventMsgConst.Discovery.EventDeviceFound)
            .observeForever { bluetooth ->
                updateDeviceList()
            }
        
        // 监听连接状态
        LiveEventBus.get<Int>(EventMsgConst.Ble.EventBleDeviceReady)
            .observeForever { model ->
                _connectionState.postValue(Ble.State.CONNECTED)
            }
        
        // 监听实时参数
        LiveEventBus.get<InterfaceEvent>(InterfaceEvent.PC60Fw.EventPC60FwRtParam)
            .observeForever { event ->
                val data = event.data
                if (data is RtParam) {
                    _realTimeParams.postValue(data)
                }
            }
    }
    
    fun startScan() {
        LepuDeviceManager.startScan()
    }
    
    fun stopScan() {
        LepuDeviceManager.stopScan()
    }
    
    fun connectDevice(context: Context, bluetooth: Bluetooth) {
        if (LepuDeviceManager.connectDevice(context, bluetooth)) {
            _connectedDevice.postValue(bluetooth)
        }
    }
    
    fun disconnectDevice() {
        LepuDeviceManager.disconnectDevice()
        _connectedDevice.postValue(null)
        _connectionState.postValue(Ble.State.DISCONNECTED)
    }
    
    private fun updateDeviceList() {
        val devices = LepuDeviceManager.getDiscoveredDevices()
        _discoveredDevices.postValue(devices)
    }
}
```

### 4. DeviceScanActivity.kt - 设备扫描界面

```kotlin
class DeviceScanActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityDeviceScanBinding
    private lateinit var deviceAdapter: DeviceListAdapter
    private lateinit var viewModel: DeviceViewModel
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDeviceScanBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        viewModel = ViewModelProvider(this)[DeviceViewModel::class.java]
        
        initViews()
        initObservers()
        checkPermissionsAndStart()
    }
    
    private fun initViews() {
        deviceAdapter = DeviceListAdapter { bluetooth ->
            viewModel.connectDevice(this, bluetooth)
        }
        
        binding.recyclerViewDevices.apply {
            adapter = deviceAdapter
            layoutManager = LinearLayoutManager(this@DeviceScanActivity)
        }
        
        binding.buttonScan.setOnClickListener {
            viewModel.startScan()
        }
        
        binding.buttonStop.setOnClickListener {
            viewModel.stopScan()
        }
    }
    
    private fun initObservers() {
        viewModel.discoveredDevices.observe(this) { devices ->
            deviceAdapter.updateDevices(devices)
            binding.textViewStatus.text = "发现 ${devices.size} 个设备"
        }
        
        viewModel.connectionState.observe(this) { state ->
            when (state) {
                Ble.State.CONNECTED -> {
                    binding.textViewStatus.text = "设备已连接"
                    // 跳转到数据采集界面
                    startActivity(Intent(this, DataCollectionActivity::class.java))
                }
                Ble.State.DISCONNECTED -> {
                    binding.textViewStatus.text = "设备已断开"
                }
            }
        }
    }
    
    private fun checkPermissionsAndStart() {
        PermissionHelper.requestBluetoothPermissions(this) { granted ->
            if (granted) {
                if (LepuDeviceManager.initialize(application)) {
                    viewModel.startScan()
                } else {
                    Toast.makeText(this, "初始化失败", Toast.LENGTH_SHORT).show()
                }
            } else {
                Toast.makeText(this, "需要蓝牙权限", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        viewModel.stopScan()
    }
}
```

### 5. PermissionHelper.kt - 权限管理工具

```kotlin
object PermissionHelper {
    
    fun requestBluetoothPermissions(
        activity: Activity, 
        callback: (Boolean) -> Unit
    ) {
        val permissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            arrayOf(
                Manifest.permission.BLUETOOTH_SCAN,
                Manifest.permission.BLUETOOTH_CONNECT,
                Manifest.permission.BLUETOOTH_ADVERTISE,
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION
            )
        } else {
            arrayOf(
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION
            )
        }
        
        PermissionX.init(activity)
            .permissions(*permissions)
            .onExplainRequestReason { scope, deniedList ->
                scope.showRequestReasonDialog(
                    deniedList,
                    "需要蓝牙和位置权限来扫描和连接设备",
                    "确定",
                    "取消"
                )
            }
            .request { allGranted, _, _ ->
                callback(allGranted)
            }
    }
}
```

## 📋 集成检查清单

### ✅ 基础配置
- [ ] 添加lepu-blepro-1.0.8.aar到libs目录
- [ ] 配置build.gradle.kts依赖
- [ ] 添加ProGuard规则
- [ ] 配置AndroidManifest.xml权限

### ✅ 核心代码
- [ ] 复制PPGManager.kt完整代码
- [ ] 创建数据模型类（PPGDataPoint等）
- [ ] 实现LepuDeviceManager
- [ ] 创建PPGDataProcessor

### ✅ UI组件
- [ ] 实现设备扫描界面
- [ ] 创建设备列表适配器
- [ ] 实现数据采集界面
- [ ] 添加波形显示控件（可选）

### ✅ 权限管理
- [ ] 实现权限申请逻辑
- [ ] 处理不同Android版本的权限差异
- [ ] 添加权限说明对话框

### ✅ 测试验证
- [ ] 测试设备扫描功能
- [ ] 测试设备连接功能
- [ ] 测试数据接收功能
- [ ] 测试PPG数据分析功能

## 🎯 快速开始步骤

1. **复制核心文件**: 将PPGManager.kt和相关bean类复制到项目中
2. **配置依赖**: 按照文档配置build.gradle.kts和权限
3. **初始化服务**: 在Application或MainActivity中调用LepuDeviceManager.initialize()
4. **实现UI**: 创建设备扫描和数据采集界面
5. **测试功能**: 逐步测试扫描、连接、数据接收功能

通过这个模板，您可以快速搭建一个完整的Lepu设备集成项目框架。
