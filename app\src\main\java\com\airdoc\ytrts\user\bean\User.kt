package com.airdoc.ytrts.user.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: User
 * Author by lilin,Date on 2025/6/19 11:00
 * PS: Not easy to write code, please indicate.
 */
@Parcelize
data class User(
    //用户id
    var id: Long? = null,
    //机构id
    var organizationId: Long? = null,
    //用户名
    var username:String? = null,
    //姓名
    var name:String? = null,
    //邮箱
    var email:String? = null,
    //性别{1=男, 2=女},可用值:1,2
    var gender:Int? = null,
    //手机国家代码
    var phoneCountryCode: Int? = null,
    //手机号
    var phoneNumber:String? = null,
    //头像
    var avatar: String? = null,
    //描述
    var description: String? = null,
    //状态{1=正常, 2=禁用},可用值:1,2
    var status: Int? = null,
    //语言
    var language: String? = null,
    //时区
    var timezone: String? = null
): Parcelable