package com.airdoc.ytrts.evaluation

import com.airdoc.component.common.cache.INameSpace

/**
 * FileName: EvaluationPreference
 * Author by lilin,Date on 2025/6/18 14:46
 * PS: Not easy to write code, please indicate.
 */
enum class EvaluationPreference(private val defaultValue:Any?) : INameSpace {

    /**
     * PPG设备 "name macAddr"
     */
    PPG_DEVICE(null);

    override fun getNameSpace(): String {
        return "EvaluationPreference"
    }

    override fun getDefaultValue(): Any? {
        return defaultValue
    }

}