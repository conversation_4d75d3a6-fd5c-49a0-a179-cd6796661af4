package com.airdoc.ytrts.ppg.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: AnalysisResult
 * Author by lilin,Date on 2025/6/20 16:23
 * PS: Not easy to write code, please indicate.
 * 分析结果数据类
 */
@Parcelize
data class AnalysisResult(
    val timeDomain: TimeDomainParameters,
    val frequencyDomain: FrequencyDomainParameters,
    val rrIntervals: List<Double>,
    val validIntervals: Int,
    val totalIntervals: Int,
    var patientId: Long = 0L
): Parcelable
