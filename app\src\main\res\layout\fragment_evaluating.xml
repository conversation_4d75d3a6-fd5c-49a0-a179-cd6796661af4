<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_ppg_data"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_ble_state"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/ic_bluetooth_not_connected"
            android:layout_marginTop="20dp"
            android:layout_marginStart="20dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_ble_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_disconnected"
            android:textColor="@color/color_1296db"
            android:textSize="12sp"
            android:layout_marginStart="10dp"
            app:layout_constraintTop_toTopOf="@+id/iv_ble_state"
            app:layout_constraintBottom_toBottomOf="@+id/iv_ble_state"
            app:layout_constraintLeft_toRightOf="@+id/iv_ble_state"/>

        <TextView
            android:id="@+id/tv_device_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_333333"
            tools:text="FD:7D:8C:53:FA:EC"
            android:textSize="14sp"
            android:layout_marginEnd="20dp"
            app:layout_constraintTop_toTopOf="@+id/iv_ble_state"
            app:layout_constraintBottom_toBottomOf="@+id/iv_ble_state"
            app:layout_constraintRight_toRightOf="parent"/>

        <TextView
            android:id="@+id/tv_spo2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="@color/color_333333"
            tools:text="SPO2：90%"
            android:layout_marginStart="20dp"
            android:layout_marginTop="20dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_ble_state"/>

        <TextView
            android:id="@+id/tv_pr"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="@color/color_333333"
            tools:text="PR：60/min"
            android:layout_marginStart="20dp"
            app:layout_constraintTop_toTopOf="@+id/tv_spo2"
            app:layout_constraintBottom_toBottomOf="@+id/tv_spo2"
            app:layout_constraintLeft_toRightOf="@+id/tv_spo2"/>

        <TextView
            android:id="@+id/tv_pi"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="@color/color_333333"
            tools:text="PI：7.6%"
            android:layout_marginStart="20dp"
            app:layout_constraintTop_toTopOf="@+id/tv_pr"
            app:layout_constraintBottom_toBottomOf="@+id/tv_pr"
            app:layout_constraintLeft_toRightOf="@+id/tv_pr"/>

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="@color/color_333333"
            tools:text="05:00"
            android:layout_marginEnd="20dp"
            android:layout_marginTop="20dp"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_device_name"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.airdoc.ytrts.evaluation.ECGView
        android:id="@+id/ecg_view"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:layout_marginTop="20dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:background="#EFF3F6"
        app:layout_constraintTop_toBottomOf="@+id/cl_ppg_data" />

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_cancel"
        android:textColor="#8D9EAC"
        android:textSize="17sp"
        android:background="@drawable/common_d6dce1_round_bg"
        android:gravity="center"
        android:layout_marginBottom="40dp"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_commit"
        app:layout_constraintHorizontal_chainStyle="packed"/>

    <TextView
        android:id="@+id/tv_commit"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_commit"
        android:textColor="@color/white"
        android:textSize="17sp"
        android:background="@drawable/common_eb4e89_round_bg"
        android:gravity="center"
        android:layout_marginBottom="40dp"
        android:layout_marginStart="30dp"
        android:visibility="gone"
        app:layout_constraintLeft_toRightOf="@+id/tv_cancel"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <TextView
        android:id="@+id/tv_terminate"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_terminate_evaluation"
        android:textColor="@color/white"
        android:textSize="17sp"
        android:background="@drawable/common_eb4e89_round_bg"
        android:gravity="center"
        android:layout_marginBottom="40dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:id="@+id/tv_retake"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_retake_evaluation"
        android:textColor="@color/white"
        android:textSize="17sp"
        android:background="@drawable/common_eb4e89_round_bg"
        android:gravity="center"
        android:visibility="gone"
        android:layout_marginBottom="40dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:id="@+id/tv_collection_state"
        android:layout_width="120dp"
        android:layout_height="40dp"
        tools:text="@string/str_data_collection_succeeded"
        android:textColor="#00FF00"
        android:textSize="17sp"
        android:gravity="center"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_marginTop="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ecg_view"/>

</androidx.constraintlayout.widget.ConstraintLayout>