package com.airdoc.ytrts.home

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothManager
import android.content.Context
import android.content.Intent
import android.location.LocationManager
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.util.SparseArray
import android.view.Gravity
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.fragment.app.commit
import com.airdoc.component.common.base.BaseCommonActivity
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.PermissionUtils
import com.airdoc.ytrts.R
import com.airdoc.ytrts.databinding.ActivityMainBinding
import com.airdoc.ytrts.home.emr.EMRManagementFragment
import com.airdoc.ytrts.home.evaluation.EvaluationManagementFragment
import com.airdoc.ytrts.user.ChangePasswordActivity
import com.airdoc.ytrts.user.LoginActivity
import com.airdoc.ytrts.user.UserInfoDialog
import com.airdoc.ytrts.user.UserPopupWindow
import com.airdoc.ytrts.user.enumeration.Gender
import com.airdoc.ytrts.user.vm.UserViewModel
import com.lepu.blepro.ext.BleServiceHelper
import com.lepu.blepro.objs.Bluetooth
import com.permissionx.guolindev.PermissionX

class MainActivity : BaseCommonActivity() {

    companion object{
        private val TAG = MainActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            val intent = Intent(context, MainActivity::class.java)
            return intent
        }
    }

    private lateinit var binding: ActivityMainBinding
    private val userVM by viewModels<UserViewModel>()

    // 注册蓝牙启用的ActivityResult回调
    private var btResultLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        Logger.d(TAG, msg = "btResultLauncher resultCode = ${result.resultCode}")
        if (result.resultCode == RESULT_OK) {
            Toast.makeText(this, "Bluetooth enabled", Toast.LENGTH_SHORT).show()
            checkLocationService()
        } else {
            Toast.makeText(this, "Bluetooth not enabled", Toast.LENGTH_SHORT).show()
        }
    }

    private var changePasswordLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK){
            //修改密码成功，需要重新登录
            startActivity(LoginActivity.createIntent(this))
            finish()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initView()
        initObserver()
        initData()
        checkPermission()
    }

    private fun initView(){
        initListener()
        showEMRManagement()
    }

    private fun initObserver(){
        userVM.userInfoLiveData.observe(this) { user ->
            binding.tvUserName.text = user?.name
            when(user?.gender){
                Gender.FEMALE.num -> {
                    binding.ivUserAvatar.setImageResource(R.drawable.ic_female_avatar_round)
                }
                else -> {
                    binding.ivUserAvatar.setImageResource(R.drawable.ic_male_avatar_round)
                }
            }
        }
        userVM.updateUserLiveData.observe(this) {
            if (it){
                userVM.getUserInfo()
                Toast.makeText(this, getString(R.string.str_modification_user_information_successful), Toast.LENGTH_SHORT).show()
            }else{
                Toast.makeText(this, getString(R.string.str_modification_user_information_failed), Toast.LENGTH_SHORT).show()
            }
        }
        userVM.logoutLiveData.observe(this) {
            if (it){
                startActivity(LoginActivity.createIntent(this))
                finish()
            }else{
                Toast.makeText(this, getString(R.string.str_logout_failed), Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun initData(){
        userVM.getUserInfo()
    }

    private fun initListener(){
        binding.tvEmrManagement.setOnSingleClickListener {
            showEMRManagement()
        }
        binding.tvEvaluationManagement.setOnSingleClickListener {
            showEvaluationManagement()
        }
        binding.clSetting.setOnSingleClickListener {
            startActivity(ProductInformationActivity.createIntent(this))
        }
        binding.llUserInfo.setOnSingleClickListener {
            UserPopupWindow(this).apply {
                showAsDropDown(binding.llUserInfo, (-20).dp2px(this@MainActivity), 0, Gravity.CENTER_VERTICAL)
                onActionClick = { action ->
                    when (action) {
                        UserPopupWindow.ACTION_USER_INFO -> {
                            UserInfoDialog(this@MainActivity).apply {
                                onOkClick = { user ->
                                    userVM.updateUserInfo(user.name.orEmpty(), user.gender?:1, user.phoneNumber.orEmpty())
                                }
                                show()
                            }
                        }
                        UserPopupWindow.ACTION_CHANGE_PASSWORD -> {
                            changePasswordLauncher.launch(ChangePasswordActivity.createIntent(this@MainActivity))
                        }
                        UserPopupWindow.ACTION_LOGOUT -> {
                            userVM.logout()
                        }
                    }
                }
            }
        }
    }

    private fun showEMRManagement(){
        binding.tvEmrManagement.isSelected = true
        binding.tvEvaluationManagement.isSelected = false
        supportFragmentManager.commit {
            supportFragmentManager.findFragmentByTag(EvaluationManagementFragment.FRAGMENT_TAG)?.also {
                hide(it)
            }
            supportFragmentManager.findFragmentByTag(EMRManagementFragment.FRAGMENT_TAG)?.also {
                show(it)
            }?:run {
                add(R.id.fl_content_container, EMRManagementFragment.newInstance(),
                    EMRManagementFragment.FRAGMENT_TAG)
            }
        }
    }

    private fun showEvaluationManagement(){
        binding.tvEmrManagement.isSelected = false
        binding.tvEvaluationManagement.isSelected = true
        supportFragmentManager.commit {
            supportFragmentManager.findFragmentByTag(EMRManagementFragment.FRAGMENT_TAG)?.also {
                hide(it)
            }
            supportFragmentManager.findFragmentByTag(EvaluationManagementFragment.FRAGMENT_TAG)?.also {
                show(it)
            }?:run {
                add(R.id.fl_content_container, EvaluationManagementFragment.newInstance(),
                    EvaluationManagementFragment.FRAGMENT_TAG)
            }
        }
    }

    /**
     * 检查权限
     */
    private fun checkPermission(){
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU){
            PermissionX.init(this)
                .permissions(
                    Manifest.permission.BLUETOOTH_SCAN,
                    Manifest.permission.BLUETOOTH_CONNECT,
                    Manifest.permission.BLUETOOTH_ADVERTISE,
                    Manifest.permission.ACCESS_FINE_LOCATION,
                    Manifest.permission.ACCESS_COARSE_LOCATION
                )
                .onExplainRequestReason { scope, deniedList ->
                    scope.showRequestReasonDialog(
                        deniedList, "location permission", "ok", "ignore"
                    )
                }
                .onForwardToSettings { scope, deniedList ->
                    scope.showForwardToSettingsDialog(
                        deniedList, "location setting", "ok", "ignore"
                    )
                }
                .request { allGranted, grantedList, deniedList ->
                    Logger.d(TAG, msg = "permission allGranted = $allGranted, grantedList = $grantedList, deniedList = $deniedList")
                    //permission OK, check Bluetooth status
                    if (allGranted){
                        checkBluetooth()
                    }
                }
        }else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PermissionX.init(this)
                .permissions(
                    Manifest.permission.BLUETOOTH_SCAN,
                    Manifest.permission.BLUETOOTH_CONNECT,
                    Manifest.permission.BLUETOOTH_ADVERTISE,
                    Manifest.permission.ACCESS_FINE_LOCATION,
                    Manifest.permission.ACCESS_COARSE_LOCATION
                )
                .onExplainRequestReason { scope, deniedList ->
                    scope.showRequestReasonDialog(
                        deniedList, "location permission", "ok", "ignore"
                    )
                }
                .onForwardToSettings { scope, deniedList ->
                    scope.showForwardToSettingsDialog(
                        deniedList, "location setting", "ok", "ignore"
                    )
                }
                .request { allGranted, grantedList, deniedList ->
                    Logger.d(TAG, msg = "permission : $allGranted, $grantedList, $deniedList")
                    //permission OK, check Bluetooth status
                    if (allGranted){
                        checkBluetooth()
                    }
                }
        }else {
            PermissionX.init(this)
                .permissions(
                    Manifest.permission.ACCESS_COARSE_LOCATION,
                    Manifest.permission.ACCESS_FINE_LOCATION,
                    Manifest.permission.BLUETOOTH,
                    Manifest.permission.BLUETOOTH_ADMIN
                )
                .onExplainRequestReason { scope, deniedList ->
                    scope.showRequestReasonDialog(
                        deniedList, "location permission", "ok", "ignore"
                    )
                }
                .onForwardToSettings { scope, deniedList ->
                    scope.showForwardToSettingsDialog(
                        deniedList, "location setting", "ok", "ignore"
                    )
                }
                .request { allGranted, grantedList, deniedList ->
                    Logger.d(TAG, msg = "permission : $allGranted, $grantedList, $deniedList")
                    //permission OK, check Bluetooth status
                    if (allGranted){
                        checkBluetooth()
                    }
                }
        }
    }

    /**
     * 检查蓝牙状态
     */
    private fun checkBluetooth(){
        val bluetoothManager = getSystemService(BLUETOOTH_SERVICE) as? BluetoothManager
        val adapter = bluetoothManager?.adapter
        if (adapter == null) {
            Toast.makeText(this, "Bluetooth is not supported", Toast.LENGTH_SHORT).show()
            return
        }
        if (!adapter.isEnabled) {
            if (PermissionUtils.checkSelfPermission(this,Manifest.permission.BLUETOOTH_CONNECT)){
                btResultLauncher.launch(Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE))
            }
        } else {
            checkLocationService()
        }
    }

    /**
     * 检查定位服务状态
     */
    private fun checkLocationService() {
        val locationEnabled = isLocationReallyEnabled(this)
        Logger.d(TAG, msg = "checkLocationService locationEnabled = $locationEnabled")
//        if (locationEnabled) {
//            LocationServiceDialog(this).apply {
//                onOkClick = {
//                    locationServiceLauncher.launch(Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS))
//                }
//            }.show()
//        } else {
//            initService()
//        }
        initService()
    }

    /**
     * 初始化指甲血氧仪服务
     */
    private fun initService() {
        val checkService = BleServiceHelper.BleServiceHelper.checkService()
        Logger.d(TAG, msg = "initService checkService = $checkService")
        if (!checkService) {
            // Save the original file path. Er1, VBeat and HHM1 are currently supported
            val rawFolders = SparseArray<String>()
            rawFolders.set(Bluetooth.MODEL_ER1, "${getExternalFilesDir(null)?.absolutePath}/er1")
            rawFolders.set(Bluetooth.MODEL_HHM1, "${getExternalFilesDir(null)?.absolutePath}/er1")
            rawFolders.set(Bluetooth.MODEL_ER1S, "${getExternalFilesDir(null)?.absolutePath}/er1")
            rawFolders.set(Bluetooth.MODEL_ER1_S, "${getExternalFilesDir(null)?.absolutePath}/er1")
            rawFolders.set(Bluetooth.MODEL_ER1_H, "${getExternalFilesDir(null)?.absolutePath}/er1")
            rawFolders.set(Bluetooth.MODEL_ER1_W, "${getExternalFilesDir(null)?.absolutePath}/er1")
            rawFolders.set(Bluetooth.MODEL_ER1_L, "${getExternalFilesDir(null)?.absolutePath}/er1")

            // initRawFolder必须在initService之前调用
            BleServiceHelper.BleServiceHelper.initRawFolder(rawFolders).initService(application).initLog(false)
        }
    }

    fun isLocationReallyEnabled(context: Context): Boolean {
        val manager = context.getSystemService(LOCATION_SERVICE) as LocationManager

        // 1. 首选官方API 28+方法
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            if (manager.isLocationEnabled) return true
        }

        // 2. 检查提供程序状态（兼容旧设备）
        val gpsEnabled = manager.isProviderEnabled(LocationManager.GPS_PROVIDER)
        val networkEnabled = manager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
        if (gpsEnabled || networkEnabled) return true

        // 3. 深度检测系统设置（针对定制ROM）
        return when {
            // 小米设备
            Build.MANUFACTURER.equals("Xiaomi", ignoreCase = true) -> {
                Settings.Secure.getInt(
                    context.contentResolver,
                    "location_providers_allowed",
                    0
                ) != 0
            }
            // 华为设备
            Build.MANUFACTURER.equals("Huawei", ignoreCase = true) -> {
                Settings.Secure.getInt(
                    context.contentResolver,
                    "location_mode",
                    0
                ) != 0
            }
            // 三星设备
            Build.MANUFACTURER.equals("Samsung", ignoreCase = true) -> {
                Settings.Global.getInt(
                    context.contentResolver,
                    "location_providers_allowed",
                    0
                ) != 0
            }
            // 其他设备回退到基础检测
            else -> false
        }
    }

}