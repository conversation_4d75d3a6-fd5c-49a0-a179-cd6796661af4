package com.airdoc.ytrts.evaluation

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.ytrts.databinding.ItemPpgDeviceBinding
import com.airdoc.ytrts.evaluation.PpgDeviceAdapter.BluetoothHolder
import com.lepu.blepro.objs.Bluetooth

/**
 * FileName: PpgDeviceAdapter
 * Author by lilin,Date on 2025/6/18 10:25
 * PS: Not easy to write code, please indicate.
 */
class PpgDeviceAdapter : RecyclerView.Adapter<BluetoothHolder>()  {

    private val bluetoothList = mutableListOf<Bluetooth>()

    fun submitList(newList: List<Bluetooth>) {
        bluetoothList.clear()
        bluetoothList.addAll(newList)
        notifyDataSetChanged()
    }
    var onItemClick:((Bluetooth) -> Unit)? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BluetoothHolder {
        return BluetoothHolder(ItemPpgDeviceBinding.inflate(LayoutInflater.from(parent.context), parent, false))
    }

    override fun onBindViewHolder(holder: BluetoothHolder, position: Int) {
        if (position in bluetoothList.indices){
            holder.bind(bluetoothList[position])
        }
    }

    override fun getItemCount(): Int {
        return bluetoothList.size
    }

    inner class BluetoothHolder(val binding: ItemPpgDeviceBinding) : RecyclerView.ViewHolder(binding.root){

        fun bind(bluetooth: Bluetooth){
            binding.apply {
                tvDeviceName.text = bluetooth.name
                root.setOnSingleClickListener {
                    onItemClick?.invoke(bluetooth)
                }
            }
        }
    }

}