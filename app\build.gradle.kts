plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    id("kotlin-android")
    id("kotlin-parcelize")
    id("kotlin-kapt")
}

android {
    namespace = "com.airdoc.ytrts"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.airdoc.ytrts"
        minSdk = 28
        targetSdk = 35
        versionCode = 100
        versionName = "1.0.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        // 指定支持的ABI
        ndk {
            abiFilters.add("arm64-v8a")
        }
    }

    buildTypes {
        release {
            //代码进行优化、混淆和缩减
            isMinifyEnabled = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            //移除未使用的资源文件
            isShrinkResources = true
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
    externalNativeBuild {
        cmake {
            path = file("src/main/cpp/CMakeLists.txt")
            version = "3.22.1"
        }
    }
    buildFeatures {
        viewBinding = true
        buildConfig = true
    }
}

dependencies {

    implementation(files("libs/lepu-blepro-1.0.8.aar"))

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    implementation(libs.androidx.constraintlayout)
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)

    //KTX
    implementation(libs.lifecycle.viewmodel.ktx)
    implementation(libs.lifecycle.livedata.ktx)
    implementation(libs.lifecycle.runtime.ktx)
    implementation(libs.fragment.ktx)
    implementation(libs.activity.ktx)
    implementation(libs.kotlinx.coroutines.android)

    //AndroidX 注解
    implementation(libs.annotation)

    //LibCommon
    implementation(libs.common)
    //WebSocket
    implementation(libs.java.websocket)
    //屏幕适配
    implementation(libs.autosize)

    //用于支持Android应用程序在使用了较多的方法数时突破Dalvik Executable (DEX) 文件的方法数限制
    implementation(libs.multidex)

    implementation(libs.gson)

    implementation(libs.mmkv.static)

    implementation(libs.glide)
    implementation(libs.glide.okhttp3.integration)
    annotationProcessor(libs.glide.compiler)
    implementation(libs.glide.transformations)

    implementation(libs.okgo)
    implementation(libs.okhttputils)

    implementation(libs.lottie)

    implementation(libs.live.event.bus.x)
    implementation(libs.lebx.processor.gson)

    //网络
    implementation(libs.okhttp)
    implementation(libs.retrofit)
    implementation(libs.retrofit.converter.gson)
    implementation(libs.okhttp.logging.interceptor)

    //CameraX
    implementation(libs.camera.core)
    implementation(libs.camera.camera2)
    implementation(libs.camera.view)
    implementation(libs.camera.lifecycle)
    implementation(libs.guava)

    //WorkManager KTX
    implementation(libs.work.runtime.ktx)

    implementation(libs.media3.exoplayer)
    implementation(libs.media3.ui)
    implementation(libs.media3.common)

    //权限请求
    implementation(libs.permissionx)

    //蓝牙
    implementation(libs.ble)

    implementation(libs.stream.log.android)
    implementation(libs.stream.log.android.file)

    //SmartRefreshLayout 下拉刷新和上拉加载
    implementation(libs.refresh.layout.kernel)
    implementation(libs.refresh.header.classics)
    implementation(libs.refresh.header.radar)
    implementation(libs.refresh.header.falsify)
    implementation(libs.refresh.header.material)
    implementation(libs.refresh.header.two.level)
    implementation(libs.refresh.footer.ball)
    implementation(libs.refresh.footer.classics)

    //电话号码工具库
    implementation(libs.libphonenumber)
    //国家代码选择器
    implementation(libs.ccp)

    //数学计算库
    implementation(libs.commons.math3)
}