package com.airdoc.ytrts.user.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.airdoc.ytrts.user.UserManager
import com.airdoc.ytrts.user.bean.LoginInfo
import com.airdoc.ytrts.user.bean.User
import com.airdoc.ytrts.user.repository.UserRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * FileName: UserViewModel
 * Author by lilin,Date on 2025/6/19 19:33
 * PS: Not easy to write code, please indicate.
 */
class UserViewModel : ViewModel() {

    private val TAG = UserViewModel::class.java.simpleName

    private val userRepository by lazy { UserRepository() }

    //当前用户信息
    val userInfoLiveData = MutableLiveData<User?>()
    //当前登录信息
    val loginInfoLiveData = MutableLiveData<Any?>()
    //登出
    val logoutLiveData = MutableLiveData<Boolean>()
    //修改用户信息
    val updateUserLiveData = MutableLiveData<Boolean>()
    //修改密码
    val changePasswordLiveData = MutableLiveData<Any?>()

    /**
     * 获取当前用户信息
     */
    fun getUserInfo(){
        viewModelScope.launch {
            MutableStateFlow(userRepository.getUserInfo(UserManager.getAuthorization())).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "getUserInfo onSuccess")
                    UserManager.setUserInfo(it)
                    userInfoLiveData.postValue(it)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "getUserInfo onDataEmpty")
                    UserManager.setUserInfo(null)
                    userInfoLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "getUserInfo onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    UserManager.setUserInfo(null)
                    userInfoLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "getUserInfo onError = $it")
                    UserManager.setUserInfo(null)
                    userInfoLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 登录
     * @param username 用户名
     * @param password 密码(加密)
     */
    fun login(username: String, password: String){
        viewModelScope.launch {
            MutableStateFlow(userRepository.login(username, password)).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "login onSuccess")
                    UserManager.setLoginInfo(it)
                    loginInfoLiveData.postValue(it)
                }
                onDataEmpty = { errorCode, errorMsg ->
                    Logger.e(TAG, msg = "login onDataEmpty")
                    UserManager.setLoginInfo(null)
                    loginInfoLiveData.postValue(Pair(errorCode,errorMsg))
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "login onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    UserManager.setLoginInfo(null)
                    loginInfoLiveData.postValue(Pair(errorCode,errorMsg))
                }
                onError = {
                    Logger.e(TAG, msg = "login onError = $it")
                    UserManager.setLoginInfo(null)
                    loginInfoLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 登出
     */
    fun logout(){
        viewModelScope.launch {
            MutableStateFlow(userRepository.logout(UserManager.getAuthorization())).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "logout onSuccess")
                    UserManager.setLoginInfo(null)
                    UserManager.setUserInfo(null)
                    logoutLiveData.postValue(true)
                }
                onDataEmpty = { _, _ ->
                    Logger.d(TAG, msg = "logout onDataEmpty")
                    UserManager.setLoginInfo(null)
                    UserManager.setUserInfo(null)
                    logoutLiveData.postValue(true)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "logout onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    logoutLiveData.postValue(false)
                }
                onError = {
                    Logger.e(TAG, msg = "logout onError = $it")
                    logoutLiveData.postValue(false)
                }
            }
        }
    }

    /**
     * 修改用户信息
     * @param name 姓名
     * @param gender 性别
     * @param phoneNumber 手机号
     */
    fun updateUserInfo(name: String, gender: Int,phoneNumber: String){
        viewModelScope.launch {
            MutableStateFlow(userRepository.updateUserInfo(name, gender,phoneNumber, UserManager.getAuthorization())).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "updateUserInfo onSuccess")
                    UserManager.getUserInfo()?.let {
                        it.name = name
                        it.gender = gender
                        it.phoneNumber = phoneNumber
                        UserManager.setUserInfo(it)
                        userInfoLiveData.postValue(it)
                    }
                    updateUserLiveData.postValue(true)
                }
                onDataEmpty = { _, _ ->
                    Logger.d(TAG, msg = "updateUserInfo onDataEmpty")
                    UserManager.getUserInfo()?.let {
                        it.name = name
                        it.gender = gender
                        it.phoneNumber = phoneNumber
                        UserManager.setUserInfo(it)
                        userInfoLiveData.postValue(it)
                    }
                    updateUserLiveData.postValue(true)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "updateUserInfo onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    updateUserLiveData.postValue(false)
                }
                onError = {
                    Logger.e(TAG, msg = "updateUserInfo onError = $it")
                    updateUserLiveData.postValue(false)
                }
            }
        }
    }

    /**
     * 修改密码
     * @param oldPassword 旧密码(加密)
     * @param newPassword 新密码(加密)
     */
    fun changePassword(oldPassword: String, newPassword: String){
        viewModelScope.launch {
            MutableStateFlow(userRepository.changePassword(oldPassword, newPassword,UserManager.getAuthorization())).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "changePassword onSuccess")
                    UserManager.setLoginInfo(null)
                    UserManager.setUserInfo(null)
                    changePasswordLiveData.postValue("SUCCESS")
                }
                onDataEmpty = { _, _ ->
                    Logger.d(TAG, msg = "changePassword onDataEmpty")
                    UserManager.setLoginInfo(null)
                    UserManager.setUserInfo(null)
                    changePasswordLiveData.postValue("SUCCESS")
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "changePassword onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    changePasswordLiveData.postValue(Pair(errorCode,errorMsg))
                }
                onError = {
                    Logger.e(TAG, msg = "changePassword onError = $it")
                    changePasswordLiveData.postValue(null)
                }
            }
        }
    }
}