package com.airdoc.ytrts.user

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.widget.Toast
import androidx.activity.viewModels
import com.airdoc.component.common.base.BaseCommonActivity
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.ytrts.R
import com.airdoc.ytrts.common.RSAManager
import com.airdoc.ytrts.databinding.ActivityLoginBinding
import com.airdoc.ytrts.home.MainActivity
import com.airdoc.ytrts.home.ProductInformationActivity
import com.airdoc.ytrts.user.bean.LoginInfo
import com.airdoc.ytrts.user.vm.UserViewModel
import kotlin.getValue

/**
 * FileName: LoginActivity
 * Author by lilin,Date on 2025/6/16 14:48
 * PS: Not easy to write code, please indicate.
 */
class LoginActivity : BaseCommonActivity() {

    companion object{
        private val TAG = LoginActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            val intent = Intent(context, LoginActivity::class.java)
            return intent
        }
    }

    private lateinit var binding: ActivityLoginBinding
    private val userVM by viewModels<UserViewModel>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initView()
        initObserver()
        UserManager.setUserInfo(null)
        UserManager.setLoginInfo(null)
    }

    private fun initView() {
        initListener()
        if (UserManager.isRememberUsernamePasswordEnable().also {
            binding.ivRememberUsernamePassword.isSelected = it
        }){
            binding.etAccountNumber.setText(UserManager.getLastLoginUserName())
            binding.etPassword.setText(UserManager.getLastLoginPassword())
        }
    }

    private fun initObserver() {
        userVM.loginInfoLiveData.observe(this){
            when (it) {
                is LoginInfo -> {
                    if (UserManager.isLogin()){
                        if (UserManager.isRememberUsernamePasswordEnable()){
                            UserManager.setLastLoginUserName(binding.etAccountNumber.text.toString().trim())
                            UserManager.setLastLoginPassword(binding.etPassword.text.toString())
                        }
                        Toast.makeText(this,getString(R.string.str_login_successfully),Toast.LENGTH_SHORT).show()
                        startActivity(MainActivity.createIntent(this))
                        finish()
                    }
                }
                is Pair<*,*> -> {
                    Toast.makeText(this,"${it.second}",Toast.LENGTH_SHORT).show()
                }
                else -> {
                    Toast.makeText(this,getString(R.string.str_login_failed),Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun initListener(){
        binding.tvLogin.setOnSingleClickListener {
            val userName = binding.etAccountNumber.text.toString().trim()
            val password = binding.etPassword.text.toString()

            // 验证账号
            val accountError = UserManager.validateAccount(userName)
            if (accountError != null) {
                Toast.makeText(this, accountError, Toast.LENGTH_SHORT).show()
                return@setOnSingleClickListener
            }

            // 验证密码
            val passwordError = UserManager.validatePassword(password)
            if (passwordError != null) {
                Toast.makeText(this, passwordError, Toast.LENGTH_SHORT).show()
                return@setOnSingleClickListener
            }

            userVM.login(userName, RSAManager.rsaEncryptWithPublicKey(password, UserManager.PASSWORD_PUBLIC_KEY))
        }
        binding.clSetting.setOnSingleClickListener {
            startActivity(ProductInformationActivity.createIntent(this))
        }

        binding.ivDisplayPassword.setOnSingleClickListener {
            binding.ivDisplayPassword.isSelected = !binding.ivDisplayPassword.isSelected
            if (binding.ivDisplayPassword.isSelected){
                binding.etPassword.transformationMethod = HideReturnsTransformationMethod.getInstance()
            }else{
                binding.etPassword.transformationMethod = PasswordTransformationMethod.getInstance()
            }
            binding.etPassword.setSelection(binding.etPassword.text.length)
        }
        binding.ivRememberUsernamePassword.setOnSingleClickListener {
            if (binding.ivRememberUsernamePassword.isSelected){
                binding.ivRememberUsernamePassword.isSelected = false
                UserManager.setRememberUsernamePasswordEnable(false)
                UserManager.setLastLoginUserName("")
                UserManager.setLastLoginPassword("")
            }else{
                binding.ivRememberUsernamePassword.isSelected = true
                UserManager.setRememberUsernamePasswordEnable(true)
            }
        }
    }

    override fun enableBack(): Boolean {
        return false
    }

}