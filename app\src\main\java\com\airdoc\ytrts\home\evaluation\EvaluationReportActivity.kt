package com.airdoc.ytrts.home.evaluation

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.print.PrintAttributes
import android.print.PrintManager
import android.webkit.WebView
import androidx.lifecycle.lifecycleScope
import com.airdoc.component.common.base.BaseCommonActivity
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.ytrts.R
import com.airdoc.ytrts.databinding.ActivityEvaluationReportBinding
import kotlinx.coroutines.launch

/**
 * FileName: EvaluationReportActivity
 * Author by lilin,Date on 2025/6/25 11:10
 * PS: Not easy to write code, please indicate.
 */
class EvaluationReportActivity : BaseCommonActivity(), EvaluationReportWebView.EvaluationActionListener{

    companion object{
        private val TAG = EvaluationReportActivity::class.java.simpleName
        const val INPUT_PARAM_REPORT_URL = "reportUrl"

        fun createIntent(context: Context,reportUrl: String): Intent {
            val intent = Intent(context, EvaluationReportActivity::class.java)
            intent.putExtra(INPUT_PARAM_REPORT_URL, reportUrl)
            return intent
        }
    }

    private lateinit var binding: ActivityEvaluationReportBinding
    private var reportUrl = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityEvaluationReportBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initParam()
        initView()
    }

    private fun initParam() {
        reportUrl = intent.getStringExtra(INPUT_PARAM_REPORT_URL) ?: ""
    }

    private fun initView() {
        binding.webEvaluation.addJavascriptInterface(binding.webEvaluation.EvaluationAction(),"android")
        binding.webEvaluation.setEvaluationActionListener(this)

        binding.webEvaluation.setBackgroundColor(Color.TRANSPARENT)

        binding.webEvaluation.loadUrl(reportUrl)
    }

    override fun onFinish() {
        lifecycleScope.launch {
            finish()
        }
    }

    override fun goHome() {
        lifecycleScope.launch {
            finish()
        }
    }

    override fun onPrintPage() {
        lifecycleScope.launch {
            createWebPrintJob(binding.webEvaluation)
        }
    }

    private fun createWebPrintJob(webView: WebView) {
        val printManager = getSystemService(PRINT_SERVICE) as PrintManager
        val jobName = getString(R.string.app_name) + " Document"

        val printAdapter = webView.createPrintDocumentAdapter(jobName)

        printManager.print(
            jobName, printAdapter,
            PrintAttributes.Builder()
                .setMediaSize(PrintAttributes.MediaSize.ISO_A4)
                .setColorMode(PrintAttributes.COLOR_MODE_COLOR)
                .setResolution(PrintAttributes.Resolution("pdf", "pdf", 600, 600))
                .setMinMargins(PrintAttributes.Margins.NO_MARGINS)
                .build()
        )
    }

}