package com.airdoc.ytrts.home.emr

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import androidx.core.graphics.drawable.toDrawable
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.ytrts.databinding.DialogSelectionAgeBinding
import java.util.Calendar

/**
 * FileName: SelectionAgeDialog
 * Author by lilin,Date on 2025/6/17 15:16
 * PS: Not easy to write code, please indicate.
 */
class SelectionAgeDialog(context: Context) : BaseCommonDialog(context) {

    companion object{
        private val TAG = SelectionAgeDialog::class.java.simpleName
    }

    private lateinit var binding: DialogSelectionAgeBinding
    var onOkClick:((year:Int,month:Int,day:Int) -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        binding = DialogSelectionAgeBinding.inflate(LayoutInflater.from(context))
        setContentView(binding.root)
        window?.apply {
            val width = 400.dp2px(context)
            val height = 330.dp2px(context)
            setLayout(width, height)
        }
        setCancelable(false)
        setCanceledOnTouchOutside(false)

        initView()
        initListener()
    }

    private fun initView() {
        val currentYear = Calendar.getInstance().get(Calendar.YEAR)
        val startYear = currentYear - 100  // 起始年份为当前年份-100年
        val endYear = currentYear          // 结束年份为当前年份

        // 设置最小日期（起始年份的1月1日）
        val calendarMin = Calendar.getInstance()
        calendarMin.set(startYear, 0, 1)
        binding.datePicker.minDate = calendarMin.timeInMillis

        // 设置最大日期（结束年份的12月31日）
        val calendarMax = Calendar.getInstance()
        calendarMax.set(endYear, 11, 31)
        binding.datePicker.maxDate = calendarMax.timeInMillis
    }

    private fun initListener() {
        binding.tvOk.setOnClickListener {
            val year = binding.datePicker.year
            val month = binding.datePicker.month + 1 // 月份从0开始（0=1月）
            val dayOfMonth = binding.datePicker.dayOfMonth
            onOkClick?.invoke(year,month,dayOfMonth)
            dismiss()
        }
        binding.tvCancel.setOnClickListener {
            dismiss()
        }
    }

    fun setDate(year:Int,month:Int,day:Int){
        binding.datePicker.init(year, month, day, null)
    }
}