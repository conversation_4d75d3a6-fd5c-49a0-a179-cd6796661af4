package com.airdoc.ytrts.home.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.airdoc.ytrts.home.bean.Patient
import com.airdoc.ytrts.home.bean.PatientAdd
import com.airdoc.ytrts.home.bean.PatientList
import com.airdoc.ytrts.home.repository.PatientRepository
import com.airdoc.ytrts.user.UserManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import java.time.LocalDate
import kotlin.random.Random

/**
 * FileName: PatientViewModel
 * Author by lilin,Date on 2025/6/17 11:18
 * PS: Not easy to write code, please indicate.
 */
class PatientViewModel : ViewModel() {

    companion object{
        private val TAG = PatientViewModel::class.java.name
    }

    private val patientRepository by lazy { PatientRepository() }

    //患者列表
    val patientListLiveData = MutableLiveData<PatientList?>()
    //添加患者
    val addPatientLiveData = MutableLiveData<PatientAdd?>()
    //修改患者
    val modifyPatientLiveData = MutableLiveData<Boolean>()
    //查询患者
    val queryPatientLiveData = MutableLiveData<Patient?>()
    //删除患者
    val deletePatientLiveData = MutableLiveData<Boolean>()

    /**
     * 获取患者列表
     * @param page 页码,从1开始
     * @param size 每页条数,示例值(10)
     * @param sort 排序条件,示例值(按创建时间倒序：createTime,desc)
     * @param gender 性别{1=男, 2=女},可用值:0,1,2,示例值(1)
     * @param keywords 搜索关键词,示例值(张三)
     */
    fun getPatientList(page:Int,size:Int = 10,sort:String? = null,gender:Int? = null,keywords:String? = null){
        viewModelScope.launch {
            MutableStateFlow(patientRepository.getPatientList(page, size,
                sort ?: "createTime,desc", gender, keywords?.ifEmpty { null }, UserManager.getAuthorization())).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "getPatientList onSuccess")
                    patientListLiveData.postValue(it)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "getPatientList onDataEmpty")
                    patientListLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "getPatientList onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    patientListLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "getPatientList onError = $it")
                    patientListLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 添加患者
     * @param name 姓名,示例值(张三)
     * @param gender 性别{1=男, 2=女},可用值:0,1,2,示例值(1)
     * @param birthday 生日,示例值(2023-04-23)
     * @param phoneNumber 手机号,示例值(13800000000)
     */
    fun addPatient(name:String,gender:Int,birthday:String, phoneNumber:String){
        viewModelScope.launch {
            MutableStateFlow(patientRepository.addPatient(name, gender, birthday, phoneNumber,
                UserManager.getAuthorization())).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "addPatient onSuccess")
                    addPatientLiveData.postValue(it)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "addPatient onDataEmpty")
                    addPatientLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "addPatient onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    addPatientLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "addPatient onError = $it")
                    addPatientLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 修改患者信息
     * @param id 患者ID,示例值(1)
     * @param name 姓名,示例值(张三)
     * @param gender 性别{1=男, 2=女},可用值:0,1,2,示例值(1)
     * @param birthday 生日,示例值(2023-04-23)
     * @param phoneNumber 手机号,示例值(13800000000)
     */
    fun modifyPatient(id:Long, name:String, gender:Int, birthday:String, phoneNumber:String){
        viewModelScope.launch {
            MutableStateFlow(patientRepository.modifyPatient(id, name, gender, birthday, phoneNumber,
                UserManager.getAuthorization())).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "modifyPatient onSuccess")
                    modifyPatientLiveData.postValue(true)
                }
                onDataEmpty = { _, _ ->
                    Logger.d(TAG, msg = "modifyPatient onDataEmpty")
                    modifyPatientLiveData.postValue(true)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "modifyPatient onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    modifyPatientLiveData.postValue(false)
                }
                onError = {
                    Logger.e(TAG, msg = "modifyPatient onError = $it")
                    modifyPatientLiveData.postValue(false)
                }
            }
        }
    }

    /**
     * 查询患者信息
     * @param id 患者ID,示例值(1)
     */
    fun queryPatient(id:Long){
        viewModelScope.launch {
            MutableStateFlow(patientRepository.queryPatient(id, UserManager.getAuthorization())).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "queryPatient onSuccess")
                    queryPatientLiveData.postValue(it)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "queryPatient onDataEmpty")
                    queryPatientLiveData.postValue(null)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "queryPatient onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    queryPatientLiveData.postValue(null)
                }
                onError = {
                    Logger.e(TAG, msg = "queryPatient onError = $it")
                    queryPatientLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 删除患者信息
     * @param ids 患者ID列表,示例值[1,2]
     */
    fun deletePatient(ids: List<Long>){
        viewModelScope.launch {
            MutableStateFlow(patientRepository.deletePatient(ids, UserManager.getAuthorization())).collectResponse {
                onSuccess = {it, _, _ ->
                    Logger.d(TAG, msg = "deletePatient onSuccess")
                    deletePatientLiveData.postValue(true)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "deletePatient onDataEmpty")
                    deletePatientLiveData.postValue(true)
                }
                onFailed = {errorCode, errorMsg ->
                    Logger.e(TAG, msg = "deletePatient onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    deletePatientLiveData.postValue(false)
                }
                onError = {
                    Logger.e(TAG, msg = "deletePatient onError = $it")
                    deletePatientLiveData.postValue(false)
                }
            }
        }
    }

    fun generateRandomChineseString(lengthRange: IntRange = 2..8): String {
        // 定义 CJK 汉字的 Unicode 范围（U+4E00 ~ U+9FFF）
        val start = 0x4E00
        val end = 0x9FFF

        // 随机确定字符串长度（2~8）
        val length = Random.nextInt(lengthRange.first, lengthRange.last + 1)

        // 生成随机汉字字符串
        return (1..length).map {
            // 生成随机码点并转换为 Char
            (Random.nextInt(start, end + 1)).toChar()
        }.joinToString("")
    }

    fun generateRandomChinesePhoneNumber(): String {
        // 手机号前两位（运营商前缀）
        val prefixes = listOf("13", "14", "15", "17", "18", "19")

        // 随机选择一个前缀
        val prefix = prefixes[Random.nextInt(prefixes.size)]

        // 生成剩余 9 位数字
        val suffix = (1..9).joinToString("") {
            Random.nextInt(0, 10).toString()
        }

        // 拼接完整手机号
        return "$prefix$suffix"
    }

    fun generateRandomDate(): String {
        val start = LocalDate.of(2020, 1, 1)
        val end = LocalDate.of(2026, 6, 17)

        // 计算总天数范围
        val daysBetween = end.toEpochDay() - start.toEpochDay()

        // 生成随机天数
        val randomDay = Random.nextInt(0, (daysBetween + 1).toInt())

        // 生成随机日期
        val randomDate = start.plusDays(randomDay.toLong())

        return randomDate.toString() // 默认格式为 "yyyy-MM-dd"
    }

}