package com.airdoc.ytrts.user

import android.content.Context
import android.content.Intent

/**
 * FileName: LoginManager
 * Author by lilin,Date on 2025/6/26 16:50
 * PS: Not easy to write code, please indicate.
 */
object LoginManager {

    private var lastRedirectTime = 0L

    fun login(context: Context) {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastRedirectTime < 2000) return
        lastRedirectTime = currentTime
        context.startActivity(LoginActivity.createIntent(context).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        })
    }

}