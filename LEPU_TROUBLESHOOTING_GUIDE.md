# Lepu设备集成故障排除指南

## 🚨 常见问题及解决方案

### 1. 初始化问题

#### 问题：BleServiceHelper初始化失败
```
错误信息: Service initialization failed
```

**解决方案：**
```kotlin
// 确保初始化顺序正确
private fun initLepuService() {
    val checkService = BleServiceHelper.BleServiceHelper.checkService()
    if (!checkService) {
        val rawFolders = SparseArray<String>()
        // 确保目录路径存在
        val dataDir = File(getExternalFilesDir(null), "pc60fw")
        if (!dataDir.exists()) {
            dataDir.mkdirs()
        }
        rawFolders.set(Bluetooth.MODEL_PC60FW, dataDir.absolutePath)
        
        // 必须按照这个顺序调用
        BleServiceHelper.BleServiceHelper
            .initRawFolder(rawFolders)  // 1. 先初始化文件夹
            .initService(application)   // 2. 再初始化服务
            .initLog(false)            // 3. 最后初始化日志
    }
}
```

#### 问题：Application context为null
```
错误信息: Context cannot be null
```

**解决方案：**
```kotlin
// 在Application的onCreate中初始化
class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        
        // 确保在主线程中初始化
        if (Looper.myLooper() == Looper.getMainLooper()) {
            initLepuService()
        } else {
            Handler(Looper.getMainLooper()).post {
                initLepuService()
            }
        }
    }
}
```

### 2. 权限问题

#### 问题：扫描不到设备
```
错误信息: No devices found during scan
```

**解决方案：**
```kotlin
// 检查所有必需权限
private fun checkAllPermissions(): Boolean {
    val requiredPermissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
        arrayOf(
            Manifest.permission.BLUETOOTH_SCAN,
            Manifest.permission.BLUETOOTH_CONNECT,
            Manifest.permission.ACCESS_FINE_LOCATION
        )
    } else {
        arrayOf(
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION
        )
    }
    
    return requiredPermissions.all { permission ->
        ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
    }
}

// 检查蓝牙和位置服务状态
private fun checkBluetoothAndLocation(): Boolean {
    val bluetoothManager = getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
    val bluetoothAdapter = bluetoothManager.adapter
    
    // 检查蓝牙是否开启
    if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled) {
        return false
    }
    
    // 检查位置服务是否开启
    val locationManager = getSystemService(Context.LOCATION_SERVICE) as LocationManager
    return locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) ||
           locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
}
```

#### 问题：Android 12+权限被拒绝
```
错误信息: Permission denied for BLUETOOTH_SCAN
```

**解决方案：**
```xml
<!-- 在AndroidManifest.xml中添加 -->
<uses-permission android:name="android.permission.BLUETOOTH_SCAN"
    android:usesPermissionFlags="neverForLocation" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
```

### 3. 设备连接问题

#### 问题：连接超时
```
错误信息: Connection timeout
```

**解决方案：**
```kotlin
// 添加连接超时处理
private fun connectWithTimeout(bluetooth: Bluetooth) {
    val timeoutHandler = Handler(Looper.getMainLooper())
    val timeoutRunnable = Runnable {
        Log.w(TAG, "连接超时，尝试重连")
        BleServiceHelper.BleServiceHelper.disconnect(false)
        // 延迟后重试
        timeoutHandler.postDelayed({
            retryConnection(bluetooth)
        }, 2000)
    }
    
    // 设置30秒超时
    timeoutHandler.postDelayed(timeoutRunnable, 30000)
    
    // 开始连接
    BleServiceHelper.BleServiceHelper.connect(this, bluetooth.model, bluetooth.device)
    
    // 连接成功后取消超时
    LiveEventBus.get<Int>(EventMsgConst.Ble.EventBleDeviceReady)
        .observe(this) {
            timeoutHandler.removeCallbacks(timeoutRunnable)
        }
}
```

#### 问题：设备频繁断开连接
```
错误信息: Device disconnected unexpectedly
```

**解决方案：**
```kotlin
// 实现自动重连机制
private var reconnectAttempts = 0
private val maxReconnectAttempts = 3

private fun handleDisconnection() {
    if (reconnectAttempts < maxReconnectAttempts) {
        reconnectAttempts++
        Log.i(TAG, "尝试重连，第${reconnectAttempts}次")
        
        Handler(Looper.getMainLooper()).postDelayed({
            val lastDevice = getLastConnectedDevice()
            lastDevice?.let { device ->
                connectDevice(device)
            }
        }, 5000) // 5秒后重连
    } else {
        Log.e(TAG, "重连失败，已达到最大重试次数")
        showConnectionFailedDialog()
    }
}
```

### 4. 数据接收问题

#### 问题：收不到实时数据
```
错误信息: No real-time data received
```

**解决方案：**
```kotlin
// 确保LiveEventBus注册正确
private fun initDataObserver() {
    // 检查是否已经注册过
    LiveEventBus.get<InterfaceEvent>(InterfaceEvent.PC60Fw.EventPC60FwRtWave)
        .observe(this) { event ->
            Log.d(TAG, "收到波形数据: ${event.data}")
            handleWaveData(event.data)
        }
    
    // 添加超时检测
    val dataTimeoutHandler = Handler(Looper.getMainLooper())
    val timeoutRunnable = Runnable {
        Log.w(TAG, "数据接收超时，检查设备连接")
        checkDeviceConnection()
    }
    
    // 10秒内没有数据则认为超时
    dataTimeoutHandler.postDelayed(timeoutRunnable, 10000)
}

private fun checkDeviceConnection() {
    // 检查设备是否仍然连接
    val connectionState = getCurrentConnectionState()
    if (connectionState != Ble.State.CONNECTED) {
        Log.w(TAG, "设备已断开，尝试重连")
        handleDisconnection()
    }
}
```

#### 问题：数据格式错误
```
错误信息: Invalid data format
```

**解决方案：**
```kotlin
private fun handleWaveData(data: Any?) {
    try {
        when (data) {
            is RtWave -> {
                val waveData = data.waveIntData
                if (waveData != null && waveData.isNotEmpty()) {
                    processValidWaveData(waveData.toList())
                } else {
                    Log.w(TAG, "波形数据为空")
                }
            }
            else -> {
                Log.w(TAG, "未知数据类型: ${data?.javaClass?.simpleName}")
            }
        }
    } catch (e: Exception) {
        Log.e(TAG, "数据处理异常: ${e.message}")
    }
}
```

### 5. PPG分析问题

#### 问题：分析结果为null
```
错误信息: PPG analysis returned null
```

**解决方案：**
```kotlin
private fun analyzePPGData(dataPoints: List<PPGDataPoint>): AnalysisResult? {
    // 检查数据点数量
    if (dataPoints.size < 1000) {
        Log.w(TAG, "数据点不足，需要至少1000个点，当前: ${dataPoints.size}")
        return null
    }
    
    // 检查数据质量
    val validDataPoints = dataPoints.filter { point ->
        point.ppgValue.isFinite() && point.ppgValue > 0 && point.timestamp > 0
    }
    
    if (validDataPoints.size < dataPoints.size * 0.8) {
        Log.w(TAG, "数据质量不佳，有效数据点比例: ${validDataPoints.size.toFloat() / dataPoints.size}")
        return null
    }
    
    return try {
        PPGManager.analyzeECG(validDataPoints)
    } catch (e: Exception) {
        Log.e(TAG, "PPG分析异常: ${e.message}")
        null
    }
}
```

#### 问题：分析结果异常
```
错误信息: Analysis results contain NaN or infinite values
```

**解决方案：**
```kotlin
private fun validateAnalysisResult(result: AnalysisResult): Boolean {
    // 检查时域参数
    val timeDomain = result.timeDomain
    if (!timeDomain.meanRR.isFinite() || 
        !timeDomain.sdnn.isFinite() || 
        !timeDomain.rmssd.isFinite()) {
        Log.w(TAG, "时域参数包含无效值")
        return false
    }
    
    // 检查频域参数
    val freqDomain = result.frequencyDomain
    if (!freqDomain.totalPower.isFinite() || 
        freqDomain.totalPower < 0) {
        Log.w(TAG, "频域参数包含无效值")
        return false
    }
    
    // 检查RR间期
    if (result.rrIntervals.any { !it.isFinite() || it <= 0 }) {
        Log.w(TAG, "RR间期包含无效值")
        return false
    }
    
    return true
}
```

### 6. 内存和性能问题

#### 问题：内存泄漏
```
错误信息: Memory leak detected
```

**解决方案：**
```kotlin
class DataCollectionActivity : AppCompatActivity() {
    
    private var dataProcessor: PPGDataProcessor? = null
    
    override fun onDestroy() {
        super.onDestroy()
        
        // 清理资源
        dataProcessor?.clearData()
        dataProcessor = null
        
        // 断开设备连接
        BleServiceHelper.BleServiceHelper.disconnect(false)
        
        // 移除LiveEventBus观察者
        LiveEventBus.get<InterfaceEvent>(InterfaceEvent.PC60Fw.EventPC60FwRtWave)
            .removeObserver(this)
    }
}
```

#### 问题：UI卡顿
```
错误信息: ANR (Application Not Responding)
```

**解决方案：**
```kotlin
// 在后台线程处理数据分析
private fun analyzeDataInBackground(dataPoints: List<PPGDataPoint>) {
    lifecycleScope.launch(Dispatchers.IO) {
        try {
            val result = PPGManager.analyzeECG(dataPoints)
            
            // 切换到主线程更新UI
            withContext(Dispatchers.Main) {
                result?.let { updateAnalysisResult(it) }
            }
        } catch (e: Exception) {
            Log.e(TAG, "后台分析异常: ${e.message}")
        }
    }
}
```

## 🔧 调试技巧

### 1. 启用详细日志
```kotlin
// 在初始化时启用日志
BleServiceHelper.BleServiceHelper.initLog(true)

// 添加自定义日志
private fun logDeviceInfo(bluetooth: Bluetooth) {
    Log.d(TAG, """
        设备信息:
        名称: ${bluetooth.name}
        地址: ${bluetooth.macAddr}
        型号: ${bluetooth.model}
        RSSI: ${bluetooth.rssi}
    """.trimIndent())
}
```

### 2. 监控连接状态
```kotlin
private fun monitorConnectionState() {
    LiveEventBus.get<Int>(EventMsgConst.Ble.EventBleDeviceReady)
        .observe(this) { model ->
            Log.d(TAG, "设备就绪: $model")
        }
    
    LiveEventBus.get<Int>(EventMsgConst.Ble.EventBleDeviceDisconnected)
        .observe(this) { model ->
            Log.d(TAG, "设备断开: $model")
        }
}
```

### 3. 数据质量检查
```kotlin
private fun checkDataQuality(dataPoints: List<PPGDataPoint>) {
    val stats = dataPoints.map { it.ppgValue }.let { values ->
        mapOf(
            "count" to values.size,
            "min" to values.minOrNull(),
            "max" to values.maxOrNull(),
            "avg" to values.average(),
            "std" to sqrt(values.map { (it - values.average()).pow(2) }.average())
        )
    }
    
    Log.d(TAG, "数据质量统计: $stats")
}
```

## 📞 技术支持

如果遇到无法解决的问题，请提供以下信息：

1. **设备信息**: Android版本、设备型号
2. **错误日志**: 完整的错误堆栈信息
3. **复现步骤**: 详细的操作步骤
4. **环境信息**: SDK版本、依赖版本
5. **测试数据**: 问题相关的测试数据

通过这份故障排除指南，您应该能够解决大部分常见的集成问题。
