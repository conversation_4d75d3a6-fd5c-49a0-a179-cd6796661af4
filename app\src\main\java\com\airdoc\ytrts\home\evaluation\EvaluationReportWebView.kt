package com.airdoc.ytrts.home.evaluation

import android.content.Context
import android.util.AttributeSet
import android.webkit.JavascriptInterface
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.ui.web.CustomWebView

/**
 * FileName: EvaluationReportWebView
 * Author by lilin,Date on 2025/6/25 19:47
 * PS: Not easy to write code, please indicate.
 */
class EvaluationReportWebView : CustomWebView {

    companion object {
        private val TAG = EvaluationReportWebView::class.java.simpleName
    }

    constructor(context: Context) : super(context, null)

    constructor(context: Context, attributeSet: AttributeSet?) : super(context, attributeSet)

    constructor(context: Context, attributeSet: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attributeSet,
        defStyleAttr
    )

    private var evaluationActionListener: EvaluationActionListener? = null

    fun setEvaluationActionListener(listener: EvaluationActionListener?){
        evaluationActionListener = listener
    }

    interface EvaluationActionListener{
        fun onFinish()
        fun goHome()
        fun onPrintPage()
    }

    inner class EvaluationAction{

        //关闭页面
        @JavascriptInterface
        fun finish(){
            Logger.d(TAG, msg = "finish")
            evaluationActionListener?.onFinish()
        }

        @JavascriptInterface
        fun goHome() {
            Logger.d(TAG, msg = "goHome")
            evaluationActionListener?.goHome()
        }

        @JavascriptInterface
        fun printPage() {
            Logger.d(TAG, msg = "printPage")
            evaluationActionListener?.onPrintPage()
        }

    }
}