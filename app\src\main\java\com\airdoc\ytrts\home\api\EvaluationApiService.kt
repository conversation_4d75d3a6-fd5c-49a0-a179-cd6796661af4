package com.airdoc.ytrts.home.api

import com.airdoc.component.common.net.entity.ApiResponse
import com.airdoc.ytrts.home.bean.EvaluationList
import com.airdoc.ytrts.home.bean.EvaluationReportResult
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.HTTP
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

/**
 * FileName: EvaluationApiService
 * Author by lilin,Date on 2025/6/20 11:47
 * PS: Not easy to write code, please indicate.
 */
interface EvaluationApiService {

    /**
     * 获取评估列表
     * @param page 页码,从1开始
     * @param size 每页条数,示例值(10)
     * @param sort 排序条件,示例值(按创建时间倒序：createTime,desc)
     * @param gender 性别{0=未知, 1=男, 2=女},可用值:0,1,2,示例值(1)
     * @param keywords 搜索关键词,示例值(张三)
     */
    @GET("re-mpd/api/assessment")
    suspend fun getEvaluationList(
        @Query("page") page:Int,
        @Query("size") size:Int,
        @Query("sort") sort:String?,
        @Query("gender") gender:Int?,
        @Query("keywords") keywords:String?,
        @Header("Authorization") authorization: String
    ): ApiResponse<EvaluationList>

    /**
     * 删除评估
     */
//    @DELETE("re-mpd/api/assessment")
    @HTTP(method = "DELETE", path = "re-mpd/api/assessment", hasBody = true)
    suspend fun deleteEvaluation(
        @Body patientReq: RequestBody,
        @Header("Authorization") authorization: String
    ): ApiResponse<Any>

    /**
     * 提交评估数据
     */
    @POST("re-mpd/api/assessment/rppg")
    suspend fun reportEvaluationData(
        @Body patientReq: RequestBody,
        @Header("Authorization") authorization: String
    ): ApiResponse<EvaluationReportResult>

}