package com.airdoc.ytrts.common

import java.security.KeyFactory
import java.security.spec.X509EncodedKeySpec
import java.util.Base64
import javax.crypto.Cipher

/**
 * FileName: RSAManager
 * Author by lilin,Date on 2025/6/19 20:30
 * PS: Not easy to write code, please indicate.
 */
object RSAManager {

    /**
     * 使用公钥对字符串进行 RSA 加密
     * @param plainText 要加密的明文
     * @param base64PublicKey 公钥的 Base64 编码
     */
    fun rsaEncryptWithPublicKey(plainText: String, base64PublicKey: String): String {
        // 1. 解码公钥
        val keyBytes = Base64.getDecoder().decode(base64PublicKey)

        // 2. 生成 RSA 公钥对象
        val keySpec = X509EncodedKeySpec(keyBytes)
        val keyFactory = KeyFactory.getInstance("RSA")
        val publicKey = keyFactory.generatePublic(keySpec)

        // 3. 初始化 Cipher
        val cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding") // 常用填充方式
        cipher.init(Cipher.ENCRYPT_MODE, publicKey)

        // 4. 加密并返回 Base64 编码的结果
        val encryptedBytes = cipher.doFinal(plainText.toByteArray(Charsets.UTF_8))
        return Base64.getEncoder().encodeToString(encryptedBytes)
    }
}