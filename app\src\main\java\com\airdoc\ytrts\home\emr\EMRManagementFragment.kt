package com.airdoc.ytrts.home.emr

import android.content.Context
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.airdoc.component.common.base.BaseBindingFragment
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.airdoc.ytrts.R
import com.airdoc.ytrts.databinding.FragmentEmrManagementBinding
import com.airdoc.ytrts.evaluation.EvaluationActivity
import com.airdoc.ytrts.home.bean.Patient
import com.airdoc.ytrts.home.vm.PatientViewModel
import com.airdoc.ytrts.user.enumeration.Gender
import com.scwang.smart.refresh.footer.ClassicsFooter
import com.scwang.smart.refresh.header.ClassicsHeader
import java.util.concurrent.atomic.AtomicBoolean

/**
 * FileName: EMRManagementFragment
 * Author by lilin,Date on 2025/6/16 16:45
 * PS: Not easy to write code, please indicate.
 * 档案管理
 */
class EMRManagementFragment : BaseBindingFragment<FragmentEmrManagementBinding>(){

    companion object{
        private val TAG = EMRManagementFragment::class.java.simpleName

        const val FRAGMENT_TAG = "EMR_MANAGEMENT"

        fun newInstance(): EMRManagementFragment {
            val fragment = EMRManagementFragment()
            return fragment
        }
    }

    private val patientVM by activityViewModels<PatientViewModel>()

    private var patients = mutableListOf<Patient>()
    private val patientAdapter = PatientAdapter()
    //是否是刷新患者列表
    private val isRefreshPatient = AtomicBoolean(false)
    //是否是加载更多患者列表
    private val isLoadMorePatient = AtomicBoolean(false)
    //患者列表页码，从1开始,0表示尚未加载数据
    private var patientPage = 0

    override fun createBinding(inflater: LayoutInflater, container: ViewGroup?): FragmentEmrManagementBinding {
        return FragmentEmrManagementBinding.inflate(inflater,container,false)
    }

    override fun initParam() {
    }

    override fun initView() {
        initListener()
        binding.rvPatient.adapter = patientAdapter
        binding.rvPatient.layoutManager =
            LinearLayoutManager(mActivity, LinearLayoutManager.VERTICAL, false)

        binding.smartRefresh.setRefreshHeader(ClassicsHeader(mActivity))
        binding.smartRefresh.setRefreshFooter(ClassicsFooter(mActivity))
        binding.smartRefresh.setEnableLoadMoreWhenContentNotFull(false)
    }

    override fun initObserver() {
        patientVM.patientListLiveData.observe(this){
            if (isRefreshPatient.get()){//刷新
                val list = it?.list.orEmpty()
                val total = it?.total?:0
                binding.smartRefresh.finishRefresh()
                patients.clear()
                patients.addAll(list)
                patientAdapter.submitList(patients)
                patientPage = 1
                if (patients.size >= total){
                    binding.smartRefresh.finishLoadMoreWithNoMoreData()
                }
                isRefreshPatient.set(false)
                binding.tvNoData.isVisible = patients.isEmpty()
            }else if (isLoadMorePatient.get()){//加载更多
                val list = it?.list
                val total = it?.total?:0
                if (!list.isNullOrEmpty()){
                    patients.addAll(list)
                    patientAdapter.addMoreItems(list)
                    patientPage++
                }
                if (patients.size >= total){
                    binding.smartRefresh.finishLoadMoreWithNoMoreData()
                }else{
                    binding.smartRefresh.finishLoadMore(true)
                }
                isLoadMorePatient.set(false)
            }else{//首次加载数据
                val list = it?.list.orEmpty()
                val total = it?.total?:0
                patients.clear()
                patients.addAll(list)
                patientAdapter.submitList(patients)
                patientPage = 1
                if (patients.size >= total){
                    binding.smartRefresh.finishLoadMoreWithNoMoreData()
                }
                binding.tvNoData.isVisible = patients.isEmpty()
            }
        }
        patientVM.modifyPatientLiveData.observe(this){
            if (it){
                val keywords = binding.etSearch.text.toString()
                loadPatient(1,keywords = keywords)
                Toast.makeText(mActivity,getString(R.string.str_medical_record_modified_successfully),Toast.LENGTH_SHORT).show()
            }else{
                Toast.makeText(mActivity,getString(R.string.str_medical_record_modified_failed),Toast.LENGTH_SHORT).show()
            }
        }
        patientVM.addPatientLiveData.observe(this){
            if (!it?.id.isNullOrEmpty()) {
                val keywords = binding.etSearch.text.toString()
                loadPatient(1,keywords = keywords)
                Toast.makeText(mActivity,getString(R.string.str_medical_record_added_successfully), Toast.LENGTH_SHORT).show()
            }else{
                Toast.makeText(mActivity,getString(R.string.str_medical_record_added_failed), Toast.LENGTH_SHORT).show()
            }
        }
        patientVM.deletePatientLiveData.observe(this){
            if (it) {
                val keywords = binding.etSearch.text.toString()
                loadPatient(1,keywords = keywords)
                Toast.makeText(mActivity,getString(R.string.str_medical_record_delete_successfully), Toast.LENGTH_SHORT).show()
            }else{
                Toast.makeText(mActivity,getString(R.string.str_medical_record_delete_failed), Toast.LENGTH_SHORT).show()
            }
        }
        patientVM.queryPatientLiveData.observe(this){
            if (it != null){
                val newMedicalRecordDialog = NewMedicalRecordDialog(mActivity,false).apply {
                    onOkClick = { patient,isNewUser ->
                        if (!isNewUser && patient.id != null){
                            patientVM.modifyPatient(patient.id!!,patient.name.orEmpty(),patient.gender?: Gender.MALE.num,
                                patient.birthday?:"",patient.phoneNumber?:"")
                        }
                    }
                }
                newMedicalRecordDialog.show()
                newMedicalRecordDialog.setPatient(it)
            }else{
                Toast.makeText(mActivity,getString(R.string.str_get_medical_record_details_failed), Toast.LENGTH_SHORT).show()
            }
        }
    }

    override fun initData() {
        loadPatient(1)
    }

    private fun initListener(){
        binding.etSearch.setOnEditorActionListener { view, actionId, event ->
            Logger.d(TAG, msg = "etSearch actionId = $actionId")
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                val keywords = binding.etSearch.text.toString()
                Logger.d(TAG, msg = "etSearch ACTION_SEARCH keywords = $keywords")
                loadPatient(1,keywords = keywords)
                hideKeyboard(view)
                // 返回 true 表示事件已处理
                true
            } else {
                // 其他动作不处理
                false
            }
        }
        binding.ivCross.setOnSingleClickListener {
            binding.etSearch.setText("")
        }
        binding.ivSearchPatients.setOnSingleClickListener {
            val keywords = binding.etSearch.text.toString()
            Logger.d(TAG, msg = "ivSearchPatients keywords = $keywords")
            loadPatient(1,keywords = keywords)
            hideKeyboard(binding.etSearch)
        }
        binding.etSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                binding.ivCross.isVisible = s.toString().isNotEmpty()
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
        binding.smartRefresh.setOnRefreshListener{
            val keywords = binding.etSearch.text.toString()
            refreshPatient(1,keywords = keywords)
        }
        binding.smartRefresh.setOnLoadMoreListener {
            val keywords = binding.etSearch.text.toString()
            loadMorePatient(patientPage + 1,keywords = keywords)
        }
        patientAdapter.onActionClick = {action,patient ->
            when(action){
                PatientAdapter.ACTION_EVALUATE -> {
                    startActivity(EvaluationActivity.createIntent(mActivity,patient))
                }
                PatientAdapter.ACTION_EDIT -> {
                    patient.id?.let {
                        patientVM.queryPatient(it)
                    }
                }
                PatientAdapter.ACTION_DELETE -> {
                    DeletePatientDialog(mActivity).apply {
                        onOkClick = {
                            patient.id?.let {
                                patientVM.deletePatient(listOf(it))
                            }
                        }
                    }.show()
                }
            }
        }
        binding.tvAddPatient.setOnSingleClickListener {
            val newMedicalRecordDialog = NewMedicalRecordDialog(mActivity,true).apply {
                onOkClick = { patient,isNewUser ->
                    if (isNewUser){
                        patientVM.addPatient(patient.name.orEmpty(),patient.gender?:Gender.MALE.num,
                            patient.birthday.orEmpty(),patient.phoneNumber.orEmpty(),
                        )
                    }
                }
            }
            newMedicalRecordDialog.show()
        }
    }

    /**
     * 加载患者列表
     */
    private fun loadPatient(page:Int,size:Int = 10,keywords:String? = null){
        Logger.d(TAG, msg = "loadPatient page = $page,size = $size")
        patientVM.getPatientList(page, size,keywords = keywords)
    }

    /**
     * 下拉刷新患者列表
     */
    private fun refreshPatient(page:Int,size:Int = 10,keywords:String? = null){
        Logger.d(TAG, msg = "refreshPatient page = $page,size = $size")
        isRefreshPatient.set(true)
        patientVM.getPatientList(page, size,keywords = keywords)
    }

    /**
     * 上划加载更多患者列表
     */
    private fun loadMorePatient(page:Int,size:Int = 10,keywords:String? = null){
        Logger.d(TAG, msg = "loadMorePatient page = $page,size = $size")
        isLoadMorePatient.set(true)
        patientVM.getPatientList(page, size,keywords = keywords)
    }

    // 关闭键盘方法（优化版）
    private fun hideKeyboard(view: View) {
        val imm = view.context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(view.windowToken, 0)
    }

}