package com.airdoc.ytrts.user

import com.airdoc.component.common.cache.INameSpace

/**
 * FileName: UserPreference
 * Author by lilin,Date on 2025/6/19 11:03
 * PS: Not easy to write code, please indicate.
 */
enum class UserPreference(private val defaultValue:Any?) : INameSpace {

    /**
     * 当前用户信息
     */
    USER_INFO(null),

    /**
     * 当前登录信息
     */
    LOGIN_INFO(null),

    /**
     * 上次登录用户名
     */
    LAST_LOGIN_USERNAME(null),

    /**
     * 上次登录密码
     */
    LAST_LOGIN_PASSWORD(null),

    /**
     * 记住用户名密码
     */
    REMEMBER_USERNAME_PASSWORD_ENABLE(false);

    override fun getNameSpace(): String {
        return "UserPreference"
    }

    override fun getDefaultValue(): Any? {
        return defaultValue
    }

}