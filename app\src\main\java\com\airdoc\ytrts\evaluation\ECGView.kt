package com.airdoc.ytrts.evaluation

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import androidx.core.view.GestureDetectorCompat
import com.airdoc.component.common.log.Logger
import kotlin.math.max
import kotlin.math.min

/**
 * FileName: ECGView
 * Author by lilin,Date on 2025/6/18 15:11
 * PS: Not easy to write code, please indicate.
 * 心电图
 */
class ECGView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    companion object {
        private val TAG = ECGView::class.java.simpleName
        private const val DEFAULT_POINT_SPACING = 5f
        private const val PADDING = 10f
        private const val BOUNDARY_PERCENT = 0.05f // 5%边界扩展
        private const val MAX_DATA_POINTS = 10000 // 最大保留数据点
    }

    enum class DisplayMode {
        REALTIME,   // 实时心电图模式：不断添加数据，禁止滑动
        STATIC      // 静态心电图模式：一次性添加数据，允许滑动
    }

    // 绘制相关属性
    private val ecgPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
        color = Color.GREEN
        strokeWidth = 3f
        strokeJoin = Paint.Join.ROUND
    }
    private val baselinePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
        color = Color.RED
        strokeWidth = 1f
    }

    // 数据管理
    private val rawData = mutableListOf<Int>()  // 存储全部原始数据
    private val ecgPath = Path()

    // 布局参数
    private var viewWidth = 0
    private var viewHeight = 0
    private var maxVisiblePoints = 0 // 一屏可见点数
    private var pointSpacing = DEFAULT_POINT_SPACING
    private var contentWidth = 0f
    private var contentHeight = 0f

    // 滑动相关属性
    private var scrollOffset = 0f // 当前滚动偏移量（像素）
    private var maxScrollOffset = 0f // 最大可滚动距离
    private val gestureDetector: GestureDetectorCompat

    // 当前可见区域的范围
    private var visibleMinValue = 0
    private var visibleMaxValue = 100

    // 显示模式
    private var displayMode = DisplayMode.REALTIME

    // 触摸事件处理状态
    private var isScrolling = false

    init {
        // 初始化手势检测器
        gestureDetector = GestureDetectorCompat(context, object : GestureDetector.SimpleOnGestureListener() {
            override fun onDown(e: MotionEvent): Boolean {
                // 必须返回true，否则后续事件不会被传递
                return displayMode == DisplayMode.STATIC && rawData.size > maxVisiblePoints
            }

            override fun onScroll(
                e1: MotionEvent?,
                e2: MotionEvent,
                distanceX: Float,
                distanceY: Float
            ): Boolean {
                if (displayMode == DisplayMode.STATIC && rawData.size > maxVisiblePoints) {
                    // 修正：改为 + distanceX 实现自然滑动方向
                    scrollOffset = (scrollOffset + distanceX).coerceIn(0f, maxScrollOffset)
                    isScrolling = true
                    rebuildPath()
                    invalidate()
                    return true
                }
                return false
            }
        })
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        viewWidth = w
        viewHeight = h

        // 计算内容区域（考虑10px内边距）
        contentWidth = viewWidth - 2 * PADDING
        contentHeight = viewHeight - 2 * PADDING

        // 计算一屏能显示的点数
        maxVisiblePoints = (contentWidth / pointSpacing).toInt()

        // 更新最大滚动距离
        updateMaxScroll()

        // 重建路径
        rebuildPath()
        invalidate()
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                isScrolling = false
            }
        }

        // 将事件传递给手势检测器
        val handled = gestureDetector.onTouchEvent(event)

        // 在静态模式下，即使手势检测器没有处理，我们也消费事件
        return if (displayMode == DisplayMode.STATIC && rawData.size > maxVisiblePoints) {
            true
        } else {
            handled
        }
    }

    /**
     * 添加多个ECG数据点
     * @param values Y轴数值列表
     */
    fun addDataPoints(values: List<Int>) {
        Logger.d(TAG, msg = "addDataPoints values = $values, visibleMinValue = $visibleMinValue, visibleMaxValue = $visibleMaxValue")
        if (values.isEmpty()) return

        // 添加新数据点
        rawData.addAll(values)

        // 如果数据量过大，移除最旧的数据
//        if (rawData.size > MAX_DATA_POINTS) {
//            rawData.subList(0, rawData.size - MAX_DATA_POINTS).clear()
//        }

        // 更新最大滚动距离
        updateMaxScroll()

        // 在实时模式下自动滚动到最新位置
        if (displayMode == DisplayMode.REALTIME && !isScrolling) {
            scrollToEnd()
        }

        // 重建路径
        rebuildPath()
        invalidate()
    }

    /**
     * 添加单个ECG数据点
     * @param value Y轴数值
     */
    fun addDataPoint(value: Int) {
        addDataPoints(listOf(value))
    }

    private fun updateMaxScroll() {
        // 计算最大滚动距离 = 总数据宽度 - 视图宽度
        val totalWidth = rawData.size * pointSpacing
        maxScrollOffset = max(0f, totalWidth - contentWidth)

        // 确保当前滚动位置在有效范围内
        scrollOffset = scrollOffset.coerceIn(0f, maxScrollOffset)
    }

    private fun rebuildPath() {
        ecgPath.reset()
        if (rawData.isEmpty()) return

        // 计算当前显示区域的起始索引
        val startIndex = if (displayMode == DisplayMode.REALTIME) {
            // 实时模式：始终显示最新数据
            max(0, rawData.size - maxVisiblePoints)
        } else {
            // 静态模式：根据滚动位置显示
            max(0, (scrollOffset / pointSpacing).toInt())
        }

        val endIndex = min(rawData.size, startIndex + maxVisiblePoints)

        // 如果数据点不足，从0开始
        if (startIndex >= rawData.size) return

        // 1. 计算可见区域的数据范围
        calculateVisibleRange(startIndex, endIndex)

        for (i in startIndex until endIndex) {
            val value = rawData[i]

            // 归一化到0-1范围（基于可见区域范围）
            val normalized = when {
                visibleMaxValue == visibleMinValue -> 0.5f
                else -> (value - visibleMinValue).toFloat() / (visibleMaxValue - visibleMinValue)
            }

            // 计算实际坐标
            val pointIndex = i - startIndex
            val x = PADDING + pointIndex * pointSpacing
            val y = PADDING + contentHeight * (1-normalized)

            if (i == startIndex) {
                ecgPath.moveTo(x, y)
            } else {
                ecgPath.lineTo(x, y)
            }
        }
    }

    private fun calculateVisibleRange(startIndex: Int, endIndex: Int) {
        if (startIndex >= endIndex) {
            visibleMinValue = 0
            visibleMaxValue = 100
            return
        }

        // 只计算可见区域的最小/最大值
//        var minVal = visibleMinValue
//        var maxVal = visibleMaxValue

        for (i in startIndex until endIndex) {
            val value = rawData[i]
            if (rawData[i] < visibleMinValue) visibleMinValue = rawData[i]
            if (rawData[i] > visibleMaxValue) visibleMaxValue = value
        }

        // 扩展边界（5%）
//        val range = (maxVal - minVal).toFloat()
//        visibleMinValue = (minVal - range * BOUNDARY_PERCENT).toInt()
//        visibleMaxValue = (maxVal + range * BOUNDARY_PERCENT).toInt()

        // 确保范围有效
        if (visibleMinValue == visibleMaxValue) {
            visibleMinValue = visibleMinValue - 1
            visibleMaxValue = visibleMaxValue + 1
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // 绘制基线（中间位置）
        val baselineY = PADDING + contentHeight / 2
        canvas.drawLine(PADDING, baselineY, PADDING + contentWidth, baselineY, baselinePaint)

        // 绘制ECG路径
        canvas.drawPath(ecgPath, ecgPaint)
    }

    /** 清除所有数据并重置视图 */
    fun clear() {
        rawData.clear()
        scrollOffset = 0f
        maxScrollOffset = 0f
        visibleMinValue = 0
        visibleMaxValue = 100
        ecgPath.reset()
        isScrolling = false
        invalidate()
    }

    /** 设置点间距（像素） */
    fun setPointSpacing(spacing: Float) {
        pointSpacing = spacing
        maxVisiblePoints = (contentWidth / pointSpacing).toInt()
        updateMaxScroll()
        rebuildPath()
        invalidate()
    }

    /** 设置显示模式 */
    fun setDisplayMode(mode: DisplayMode) {
        displayMode = mode

        // 实时模式：自动滚动到最新位置
        if (mode == DisplayMode.REALTIME) {
            scrollToEnd()
        }

        // 重建路径
        rebuildPath()
        invalidate()
    }

    /** 获取当前显示模式 */
    fun getDisplayMode(): DisplayMode = displayMode

    /** 滚动到最新位置 */
    fun scrollToEnd() {
        scrollOffset = maxScrollOffset
        rebuildPath()
        invalidate()
    }

    /** 获取当前数据点数量 */
    fun getDataCount(): Int = rawData.size

    /** 获取当前滚动位置（0.0-1.0） */
    fun getScrollPosition(): Float {
        return if (maxScrollOffset > 0) scrollOffset / maxScrollOffset else 0f
    }

    /** 设置滚动位置（0.0-1.0） */
    fun setScrollPosition(position: Float) {
        if (displayMode == DisplayMode.STATIC && rawData.size > maxVisiblePoints) {
            scrollOffset = position.coerceIn(0f, 1f) * maxScrollOffset
            rebuildPath()
            invalidate()
        }
    }

}