package com.airdoc.ytrts.home.evaluation

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import androidx.core.graphics.drawable.toDrawable
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.ytrts.databinding.DialogDeleteEvaluationBinding

/**
 * FileName: DeleteEvaluationDialog
 * Author by lilin,Date on 2025/6/20 14:27
 * PS: Not easy to write code, please indicate.
 */
class DeleteEvaluationDialog(context: Context) : BaseCommonDialog(context) {
    companion object{
        private val TAG = DeleteEvaluationDialog::class.java.simpleName
    }

    private lateinit var binding: DialogDeleteEvaluationBinding
    var onOkClick:(() -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        binding = DialogDeleteEvaluationBinding.inflate(LayoutInflater.from(context))
        setContentView(binding.root)
        window?.apply {
            val width = 400.dp2px(context)
            val height = 200.dp2px(context)
            setLayout(width, height)
        }
        setCancelable(false)
        setCanceledOnTouchOutside(false)

        initView()
        initListener()
    }

    private fun initView(){

    }

    private fun initListener(){
        binding.tvOk.setOnSingleClickListener {
            onOkClick?.invoke()
            dismiss()
        }
        binding.tvCancel.setOnSingleClickListener {
            dismiss()
        }
    }
}