<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #2c3e50; }
      .module-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .class-name { font-family: Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .description { font-family: Arial, sans-serif; font-size: 9px; fill: #7f8c8d; }
      .module-box { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; }
      .app-box { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .activity-box { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .fragment-box { fill: #f39c12; stroke: #e67e22; stroke-width: 2; }
      .viewmodel-box { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; }
      .repository-box { fill: #1abc9c; stroke: #16a085; stroke-width: 2; }
      .manager-box { fill: #27ae60; stroke: #229954; stroke-width: 2; }
      .bean-box { fill: #f1c40f; stroke: #f39c12; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .dependency-arrow { stroke: #e67e22; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" class="title">YT-RTS Android App Architecture</text>
  <text x="700" y="50" text-anchor="middle" class="description">医疗设备PPG数据采集与分析系统</text>
  
  <!-- Application Layer -->
  <rect x="50" y="80" width="200" height="60" class="app-box" rx="5"/>
  <text x="150" y="100" text-anchor="middle" class="module-title" fill="white">Application</text>
  <text x="150" y="115" text-anchor="middle" class="class-name" fill="white">YtRtsApplication</text>
  <text x="150" y="130" text-anchor="middle" class="description" fill="white">应用初始化</text>
  
  <!-- Launcher -->
  <rect x="300" y="80" width="150" height="60" class="activity-box" rx="5"/>
  <text x="375" y="100" text-anchor="middle" class="module-title" fill="white">Launcher</text>
  <text x="375" y="115" text-anchor="middle" class="class-name" fill="white">LauncherActivity</text>
  <text x="375" y="130" text-anchor="middle" class="description" fill="white">启动页</text>
  
  <!-- User Module -->
  <rect x="50" y="180" width="300" height="180" class="module-box" rx="5"/>
  <text x="200" y="200" text-anchor="middle" class="module-title">User Module (用户模块)</text>
  
  <!-- User Activities -->
  <rect x="70" y="220" width="120" height="40" class="activity-box" rx="3"/>
  <text x="130" y="235" text-anchor="middle" class="class-name" fill="white">LoginActivity</text>
  <text x="130" y="250" text-anchor="middle" class="description" fill="white">登录页面</text>
  
  <rect x="210" y="220" width="120" height="40" class="activity-box" rx="3"/>
  <text x="270" y="235" text-anchor="middle" class="class-name" fill="white">ChangePasswordActivity</text>
  <text x="270" y="250" text-anchor="middle" class="description" fill="white">修改密码</text>
  
  <!-- User ViewModel -->
  <rect x="70" y="280" width="120" height="40" class="viewmodel-box" rx="3"/>
  <text x="130" y="295" text-anchor="middle" class="class-name" fill="white">UserViewModel</text>
  <text x="130" y="310" text-anchor="middle" class="description" fill="white">用户业务逻辑</text>
  
  <!-- User Repository -->
  <rect x="210" y="280" width="120" height="40" class="repository-box" rx="3"/>
  <text x="270" y="295" text-anchor="middle" class="class-name" fill="white">UserRepository</text>
  <text x="270" y="310" text-anchor="middle" class="description" fill="white">用户数据层</text>
  
  <!-- User Manager -->
  <rect x="140" y="330" width="120" height="40" class="manager-box" rx="3"/>
  <text x="200" y="345" text-anchor="middle" class="class-name" fill="white">UserManager</text>
  <text x="200" y="360" text-anchor="middle" class="description" fill="white">用户管理</text>
  
  <!-- Home Module -->
  <rect x="400" y="180" width="350" height="180" class="module-box" rx="5"/>
  <text x="575" y="200" text-anchor="middle" class="module-title">Home Module (主页模块)</text>
  
  <!-- Main Activity -->
  <rect x="420" y="220" width="120" height="40" class="activity-box" rx="3"/>
  <text x="480" y="235" text-anchor="middle" class="class-name" fill="white">MainActivity</text>
  <text x="480" y="250" text-anchor="middle" class="description" fill="white">主页面</text>
  
  <!-- Home Fragments -->
  <rect x="560" y="220" width="170" height="40" class="fragment-box" rx="3"/>
  <text x="645" y="235" text-anchor="middle" class="class-name" fill="white">EMRManagementFragment</text>
  <text x="645" y="250" text-anchor="middle" class="description" fill="white">病历管理</text>
  
  <rect x="420" y="280" width="170" height="40" class="fragment-box" rx="3"/>
  <text x="505" y="295" text-anchor="middle" class="class-name" fill="white">EvaluationManagementFragment</text>
  <text x="505" y="310" text-anchor="middle" class="description" fill="white">评估管理</text>
  
  <!-- Home ViewModels -->
  <rect x="610" y="280" width="120" height="40" class="viewmodel-box" rx="3"/>
  <text x="670" y="295" text-anchor="middle" class="class-name" fill="white">PatientViewModel</text>
  <text x="670" y="310" text-anchor="middle" class="description" fill="white">患者业务逻辑</text>
  
  <rect x="420" y="330" width="120" height="40" class="viewmodel-box" rx="3"/>
  <text x="480" y="345" text-anchor="middle" class="class-name" fill="white">EvaluationViewModel</text>
  <text x="480" y="360" text-anchor="middle" class="description" fill="white">评估业务逻辑</text>
  
  <!-- Home Repository -->
  <rect x="560" y="330" width="170" height="40" class="repository-box" rx="3"/>
  <text x="645" y="345" text-anchor="middle" class="class-name" fill="white">EvaluationRepository</text>
  <text x="645" y="360" text-anchor="middle" class="description" fill="white">评估数据层</text>
  
  <!-- Evaluation Module -->
  <rect x="800" y="180" width="300" height="180" class="module-box" rx="5"/>
  <text x="950" y="200" text-anchor="middle" class="module-title">Evaluation Module (评估模块)</text>
  
  <!-- Evaluation Activity -->
  <rect x="820" y="220" width="120" height="40" class="activity-box" rx="3"/>
  <text x="880" y="235" text-anchor="middle" class="class-name" fill="white">EvaluationActivity</text>
  <text x="880" y="250" text-anchor="middle" class="description" fill="white">评估页面</text>
  
  <!-- Evaluation Fragments -->
  <rect x="960" y="220" width="120" height="40" class="fragment-box" rx="3"/>
  <text x="1020" y="235" text-anchor="middle" class="class-name" fill="white">EvaluationInitFragment</text>
  <text x="1020" y="250" text-anchor="middle" class="description" fill="white">评估初始化</text>
  
  <rect x="820" y="280" width="120" height="40" class="fragment-box" rx="3"/>
  <text x="880" y="295" text-anchor="middle" class="class-name" fill="white">EvaluatingFragment</text>
  <text x="880" y="310" text-anchor="middle" class="description" fill="white">评估进行中</text>
  
  <!-- PPG Module -->
  <rect x="50" y="400" width="350" height="180" class="module-box" rx="5"/>
  <text x="225" y="420" text-anchor="middle" class="module-title">PPG Module (PPG数据处理模块)</text>
  
  <!-- PPG ViewModel -->
  <rect x="70" y="440" width="120" height="40" class="viewmodel-box" rx="3"/>
  <text x="130" y="455" text-anchor="middle" class="class-name" fill="white">PpgViewModel</text>
  <text x="130" y="470" text-anchor="middle" class="description" fill="white">PPG业务逻辑</text>
  
  <!-- PPG Manager -->
  <rect x="210" y="440" width="120" height="40" class="manager-box" rx="3"/>
  <text x="270" y="455" text-anchor="middle" class="class-name" fill="white">PPGManager</text>
  <text x="270" y="470" text-anchor="middle" class="description" fill="white">PPG数据分析</text>
  
  <!-- PPG Beans -->
  <rect x="70" y="500" width="120" height="40" class="bean-box" rx="3"/>
  <text x="130" y="515" text-anchor="middle" class="class-name">PPGDataPoint</text>
  <text x="130" y="530" text-anchor="middle" class="description">PPG数据点</text>
  
  <rect x="210" y="500" width="120" height="40" class="bean-box" rx="3"/>
  <text x="270" y="515" text-anchor="middle" class="class-name">AnalysisResult</text>
  <text x="270" y="530" text-anchor="middle" class="description">分析结果</text>
  
  <!-- Device Manager -->
  <rect x="70" y="540" width="260" height="40" class="manager-box" rx="3"/>
  <text x="200" y="555" text-anchor="middle" class="class-name" fill="white">DeviceManager & BLE Service</text>
  <text x="200" y="570" text-anchor="middle" class="description" fill="white">设备管理与蓝牙服务</text>
  
  <!-- Network Module -->
  <rect x="450" y="400" width="300" height="180" class="module-box" rx="5"/>
  <text x="600" y="420" text-anchor="middle" class="module-title">Network Module (网络模块)</text>
  
  <!-- Network Components -->
  <rect x="470" y="440" width="120" height="40" class="repository-box" rx="3"/>
  <text x="530" y="455" text-anchor="middle" class="class-name" fill="white">MainRetrofitClient</text>
  <text x="530" y="470" text-anchor="middle" class="description" fill="white">网络客户端</text>
  
  <rect x="610" y="440" width="120" height="40" class="repository-box" rx="3"/>
  <text x="670" y="455" text-anchor="middle" class="class-name" fill="white">ApiService</text>
  <text x="670" y="470" text-anchor="middle" class="description" fill="white">API接口</text>
  
  <rect x="470" y="500" width="120" height="40" class="manager-box" rx="3"/>
  <text x="530" y="515" text-anchor="middle" class="class-name" fill="white">UrlConfig</text>
  <text x="530" y="530" text-anchor="middle" class="description">URL配置</text>
  
  <rect x="610" y="500" width="120" height="40" class="repository-box" rx="3"/>
  <text x="670" y="515" text-anchor="middle" class="class-name" fill="white">Interceptors</text>
  <text x="670" y="530" text-anchor="middle" class="description" fill="white">拦截器</text>
  
  <!-- Data Models -->
  <rect x="800" y="400" width="300" height="180" class="module-box" rx="5"/>
  <text x="950" y="420" text-anchor="middle" class="module-title">Data Models (数据模型)</text>
  
  <!-- User Beans -->
  <rect x="820" y="440" width="120" height="40" class="bean-box" rx="3"/>
  <text x="880" y="455" text-anchor="middle" class="class-name">User</text>
  <text x="880" y="470" text-anchor="middle" class="description">用户信息</text>
  
  <rect x="960" y="440" width="120" height="40" class="bean-box" rx="3"/>
  <text x="1020" y="455" text-anchor="middle" class="class-name">LoginInfo</text>
  <text x="1020" y="470" text-anchor="middle" class="description">登录信息</text>
  
  <!-- Evaluation Beans -->
  <rect x="820" y="500" width="120" height="40" class="bean-box" rx="3"/>
  <text x="880" y="515" text-anchor="middle" class="class-name">Patient</text>
  <text x="880" y="530" text-anchor="middle" class="description">患者信息</text>
  
  <rect x="960" y="500" width="120" height="40" class="bean-box" rx="3"/>
  <text x="1020" y="515" text-anchor="middle" class="class-name">Evaluation</text>
  <text x="1020" y="530" text-anchor="middle" class="description">评估报告</text>
  
  <!-- Common Components -->
  <rect x="50" y="620" width="1050" height="80" class="module-box" rx="5"/>
  <text x="575" y="640" text-anchor="middle" class="module-title">Common Components (公共组件)</text>
  
  <rect x="70" y="660" width="120" height="30" class="manager-box" rx="3"/>
  <text x="130" y="680" text-anchor="middle" class="class-name" fill="white">MMKVManager</text>
  
  <rect x="210" y="660" width="120" height="30" class="manager-box" rx="3"/>
  <text x="270" y="680" text-anchor="middle" class="class-name" fill="white">Logger</text>
  
  <rect x="350" y="660" width="120" height="30" class="manager-box" rx="3"/>
  <text x="410" y="680" text-anchor="middle" class="class-name" fill="white">NetworkManager</text>
  
  <rect x="490" y="660" width="120" height="30" class="manager-box" rx="3"/>
  <text x="550" y="680" text-anchor="middle" class="class-name" fill="white">PermissionUtils</text>
  
  <rect x="630" y="660" width="120" height="30" class="manager-box" rx="3"/>
  <text x="690" y="680" text-anchor="middle" class="class-name" fill="white">LiveEventBus</text>
  
  <rect x="770" y="660" width="120" height="30" class="manager-box" rx="3"/>
  <text x="830" y="680" text-anchor="middle" class="class-name" fill="white">AutoSizeConfig</text>
  
  <rect x="910" y="660" width="120" height="30" class="manager-box" rx="3"/>
  <text x="970" y="680" text-anchor="middle" class="class-name" fill="white">BaseComponents</text>
  
  <!-- Architecture Flow Arrows -->
  <line x1="250" y1="110" x2="300" y2="110" class="arrow"/>
  <line x1="450" y1="110" x2="480" y2="180" class="arrow"/>
  <line x1="375" y1="140" x2="200" y2="180" class="arrow"/>
  
  <!-- Module Dependencies -->
  <line x1="540" y1="240" x2="820" y2="240" class="dependency-arrow"/>
  <line x1="880" y1="260" x2="880" y2="400" class="dependency-arrow"/>
  <line x1="330" y1="300" x2="420" y2="300" class="dependency-arrow"/>
  <line x1="270" y1="320" x2="470" y2="400" class="dependency-arrow"/>
  
  <!-- Legend -->
  <rect x="1150" y="80" width="200" height="300" class="module-box" rx="5"/>
  <text x="1250" y="100" text-anchor="middle" class="module-title">图例</text>
  
  <rect x="1170" y="120" width="30" height="15" class="activity-box" rx="2"/>
  <text x="1210" y="132" class="description">Activity</text>
  
  <rect x="1170" y="145" width="30" height="15" class="fragment-box" rx="2"/>
  <text x="1210" y="157" class="description">Fragment</text>
  
  <rect x="1170" y="170" width="30" height="15" class="viewmodel-box" rx="2"/>
  <text x="1210" y="182" class="description">ViewModel</text>
  
  <rect x="1170" y="195" width="30" height="15" class="repository-box" rx="2"/>
  <text x="1210" y="207" class="description">Repository</text>
  
  <rect x="1170" y="220" width="30" height="15" class="manager-box" rx="2"/>
  <text x="1210" y="232" class="description">Manager</text>
  
  <rect x="1170" y="245" width="30" height="15" class="bean-box" rx="2"/>
  <text x="1210" y="257" class="description">Data Model</text>
  
  <line x1="1170" y1="275" x2="1200" y2="275" class="arrow"/>
  <text x="1210" y="280" class="description">继承/调用</text>
  
  <line x1="1170" y1="295" x2="1200" y2="295" class="dependency-arrow"/>
  <text x="1210" y="300" class="description">依赖关系</text>
  
  <!-- Architecture Description -->
  <text x="50" y="750" class="module-title">架构说明:</text>
  <text x="50" y="770" class="description">1. 采用MVVM架构模式，Activity/Fragment + ViewModel + Repository</text>
  <text x="50" y="785" class="description">2. 用户模块负责登录认证和用户信息管理</text>
  <text x="50" y="800" class="description">3. 主页模块包含病历管理和评估管理功能</text>
  <text x="50" y="815" class="description">4. 评估模块负责PPG设备连接和数据采集</text>
  <text x="50" y="830" class="description">5. PPG模块处理光电容积脉搏波数据分析和心率变异性计算</text>
  <text x="50" y="845" class="description">6. 网络模块提供API接口和数据传输服务</text>
  <text x="50" y="860" class="description">7. 公共组件提供基础功能支持</text>
  
</svg>
