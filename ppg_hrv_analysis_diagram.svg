<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .code { font-family: 'Courier New', monospace; font-size: 10px; fill: #e74c3c; }
      .box { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; rx: 5; }
      .process-box { fill: #3498db; stroke: #2980b9; stroke-width: 2; rx: 5; }
      .result-box { fill: #2ecc71; stroke: #27ae60; stroke-width: 2; rx: 5; }
      .wave-line { stroke: #e74c3c; stroke-width: 2; fill: none; }
      .peak-point { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: #34495e; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" class="title">PPG波形绘制与HRV分析流程图</text>

  <!-- 第一部分：数据接收与处理 -->
  <rect x="50" y="60" width="300" height="120" class="box"/>
  <text x="200" y="80" text-anchor="middle" class="subtitle">1. 数据接收与处理</text>
  
  <text x="60" y="100" class="text">LiveEventBus监听事件:</text>
  <text x="60" y="115" class="code">EventPC60FwRtWave</text>
  <text x="60" y="130" class="text">获取 RtWave.waveIntData</text>
  <text x="60" y="145" class="small-text">原始PPG信号数据 (Int数组)</text>
  <text x="60" y="160" class="text">创建PPGDataPoint:</text>
  <text x="60" y="175" class="code">value.toDouble() + timestamp</text>

  <!-- 第二部分：波形绘制 -->
  <rect x="400" y="60" width="350" height="120" class="process-box"/>
  <text x="575" y="80" text-anchor="middle" class="subtitle" fill="white">2. ECGView波形绘制</text>
  
  <text x="410" y="100" class="text" fill="white">addDataPoints(ints) → rawData</text>
  <text x="410" y="115" class="text" fill="white">rebuildPath() 重建绘制路径</text>
  <text x="410" y="130" class="text" fill="white">数据归一化: (value-min)/(max-min)</text>
  <text x="410" y="145" class="text" fill="white">坐标计算: x=index*spacing, y=height*(1-norm)</text>
  <text x="410" y="160" class="text" fill="white">Canvas.drawPath() 绘制波形</text>

  <!-- 模拟PPG波形 -->
  <g transform="translate(800, 60)">
    <rect x="0" y="0" width="350" height="120" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1" rx="3"/>
    <text x="175" y="15" text-anchor="middle" class="small-text">实时PPG波形显示</text>
    
    <!-- 绘制模拟PPG波形 -->
    <path d="M20,80 Q30,60 40,80 Q50,100 60,80 Q70,50 80,80 Q90,110 100,80 Q110,45 120,80 Q130,115 140,80 Q150,40 160,80 Q170,120 180,80 Q190,35 200,80 Q210,125 220,80 Q230,30 240,80 Q250,130 260,80 Q270,25 280,80 Q290,135 300,80 Q310,20 320,80" class="wave-line"/>
    
    <!-- 标记R波峰值 -->
    <circle cx="70" cy="50" r="3" class="peak-point"/>
    <circle cx="150" cy="40" r="3" class="peak-point"/>
    <circle cx="230" cy="30" r="3" class="peak-point"/>
    <circle cx="310" cy="20" r="3" class="peak-point"/>
    
    <text x="70" y="45" text-anchor="middle" class="small-text">R1</text>
    <text x="150" y="35" text-anchor="middle" class="small-text">R2</text>
    <text x="230" y="25" text-anchor="middle" class="small-text">R3</text>
    <text x="310" y="15" text-anchor="middle" class="small-text">R4</text>
  </g>

  <!-- 箭头连接 -->
  <line x1="350" y1="120" x2="400" y2="120" class="arrow"/>
  <line x1="750" y1="120" x2="800" y2="120" class="arrow"/>

  <!-- 第三部分：HRV分析开始 -->
  <rect x="50" y="220" width="300" height="100" class="result-box"/>
  <text x="200" y="240" text-anchor="middle" class="subtitle" fill="white">3. HRV分析入口</text>
  
  <text x="60" y="260" class="text" fill="white">completeEvaluation()触发</text>
  <text x="60" y="275" class="code" fill="white">PPGManager.analyzeECG(ppgDataPointList)</text>
  <text x="60" y="290" class="text" fill="white">输入: List&lt;PPGDataPoint&gt;</text>
  <text x="60" y="305" class="text" fill="white">包含PPG值和时间戳</text>

  <!-- 第四部分：R波检测 -->
  <rect x="400" y="220" width="350" height="100" class="process-box"/>
  <text x="575" y="240" text-anchor="middle" class="subtitle" fill="white">4. R波检测算法</text>
  
  <text x="410" y="260" class="text" fill="white">多策略峰值检测:</text>
  <text x="410" y="275" class="text" fill="white">• 统计阈值策略 (mean + 1.2*std)</text>
  <text x="410" y="290" class="text" fill="white">• 最大值策略 (max * 0.7)</text>
  <text x="410" y="305" class="text" fill="white">• 自适应阈值 + 导数检测</text>

  <!-- 第五部分：RR间期计算 -->
  <rect x="800" y="220" width="350" height="100" class="box"/>
  <text x="975" y="240" text-anchor="middle" class="subtitle">5. RR间期计算</text>
  
  <text x="810" y="260" class="text">使用实际时间戳计算:</text>
  <text x="810" y="275" class="code">RR = (timestamp2 - timestamp1) / 1000000</text>
  <text x="810" y="290" class="text">有效范围: 600ms - 1000ms</text>
  <text x="810" y="305" class="text">对应心率: 60-100 BPM</text>

  <!-- RR间期示意图 -->
  <g transform="translate(50, 350)">
    <rect x="0" y="0" width="500" height="80" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1" rx="3"/>
    <text x="250" y="15" text-anchor="middle" class="small-text">RR间期示意图</text>
    
    <!-- 简化的心跳波形 -->
    <path d="M50,50 L80,50 L90,30 L100,50 L150,50 L160,25 L170,50 L220,50 L230,35 L240,50 L290,50 L300,20 L310,50 L360,50" class="wave-line"/>
    
    <!-- RR间期标注 -->
    <line x1="90" y1="60" x2="160" y2="60" stroke="#2ecc71" stroke-width="2"/>
    <text x="125" y="75" text-anchor="middle" class="small-text">RR1 = 850ms</text>
    
    <line x1="160" y1="60" x2="230" y2="60" stroke="#2ecc71" stroke-width="2"/>
    <text x="195" y="75" text-anchor="middle" class="small-text">RR2 = 820ms</text>
    
    <line x1="230" y1="60" x2="300" y2="60" stroke="#2ecc71" stroke-width="2"/>
    <text x="265" y="75" text-anchor="middle" class="small-text">RR3 = 780ms</text>
  </g>

  <!-- 第六部分：时域参数计算 -->
  <rect x="600" y="350" width="250" height="120" class="result-box"/>
  <text x="725" y="370" text-anchor="middle" class="subtitle" fill="white">6. 时域参数</text>
  
  <text x="610" y="390" class="text" fill="white">• MeanNN: 平均RR间期</text>
  <text x="610" y="405" class="text" fill="white">• SDNN: RR间期标准差</text>
  <text x="610" y="420" class="text" fill="white">• RMSSD: 相邻RR差值均方根</text>
  <text x="610" y="435" class="text" fill="white">• pNN50: >50ms差值百分比</text>
  <text x="610" y="450" class="code" fill="white">sqrt(Σ(RRi-mean)²/n)</text>

  <!-- 第七部分：频域参数计算 -->
  <rect x="900" y="350" width="250" height="120" class="process-box"/>
  <text x="1025" y="370" text-anchor="middle" class="subtitle" fill="white">7. 频域参数</text>
  
  <text x="910" y="390" class="text" fill="white">• VLF: 0.003-0.04 Hz</text>
  <text x="910" y="405" class="text" fill="white">• LF: 0.04-0.15 Hz</text>
  <text x="910" y="420" class="text" fill="white">• HF: 0.15-0.4 Hz</text>
  <text x="910" y="435" class="text" fill="white">• LF/HF比值</text>
  <text x="910" y="450" class="text" fill="white">FFT功率谱分析</text>

  <!-- 连接箭头 -->
  <line x1="200" y1="320" x2="200" y2="350" class="arrow"/>
  <line x1="350" y1="270" x2="400" y2="270" class="arrow"/>
  <line x1="750" y1="270" x2="800" y2="270" class="arrow"/>
  <line x1="550" y1="390" x2="600" y2="390" class="arrow"/>
  <line x1="850" y1="410" x2="900" y2="410" class="arrow"/>

  <!-- 最终结果 -->
  <rect x="400" y="520" width="400" height="100" class="result-box"/>
  <text x="600" y="540" text-anchor="middle" class="subtitle" fill="white">8. 分析结果 (AnalysisResult)</text>
  
  <text x="410" y="560" class="text" fill="white">• timeDomain: TimeDomainParameters</text>
  <text x="410" y="575" class="text" fill="white">• frequencyDomain: FrequencyDomainParameters</text>
  <text x="410" y="590" class="text" fill="white">• rrIntervals: List&lt;Double&gt;</text>
  <text x="410" y="605" class="text" fill="white">• validIntervals / totalIntervals</text>

  <!-- 最终箭头 -->
  <line x1="725" y1="470" x2="600" y2="520" class="arrow"/>
  <line x1="1025" y1="470" x2="600" y2="520" class="arrow"/>

  <!-- 技术说明 -->
  <rect x="50" y="650" width="1100" height="120" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1" rx="3"/>
  <text x="600" y="670" text-anchor="middle" class="subtitle">技术实现要点</text>
  
  <text x="60" y="690" class="text">1. 数据流: 硬件设备 → LiveEventBus → EvaluatingFragment → ECGView实时绘制</text>
  <text x="60" y="705" class="text">2. 时间戳精度: 纳秒级时间戳，20ms采样间隔，确保RR间期计算准确性</text>
  <text x="60" y="720" class="text">3. 多策略R波检测: 统计阈值+最大值+自适应+导数，提高检测鲁棒性</text>
  <text x="60" y="735" class="text">4. HRV标准算法: 符合国际HRV分析标准，时域+频域完整参数</text>
  <text x="60" y="750" class="text">5. 实时性能: 边采集边显示，评估完成后统一分析，平衡实时性与准确性</text>

</svg>
