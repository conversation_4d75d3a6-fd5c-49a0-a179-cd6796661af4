package com.airdoc.ytrts.home.evaluation

import android.content.Context
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.airdoc.component.common.base.BaseBindingFragment
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.airdoc.ytrts.R
import com.airdoc.ytrts.databinding.FragmentEvaluationManagementBinding
import com.airdoc.ytrts.home.bean.Evaluation
import com.airdoc.ytrts.home.vm.EvaluationViewModel
import com.scwang.smart.refresh.footer.ClassicsFooter
import com.scwang.smart.refresh.header.ClassicsHeader
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.collections.orEmpty
import kotlin.getValue

/**
 * FileName: EvaluationManagementFragment
 * Author by lilin,Date on 2025/6/17 16:20
 * PS: Not easy to write code, please indicate.
 */
class EvaluationManagementFragment : BaseBindingFragment<FragmentEvaluationManagementBinding>(){

    companion object {
        private val TAG = EvaluationManagementFragment::class.java.simpleName
        const val FRAGMENT_TAG = "EVALUATION_MANAGEMENT"
        fun newInstance(): EvaluationManagementFragment {
            return EvaluationManagementFragment()
        }
    }

    private val evaluationVM by activityViewModels<EvaluationViewModel>()

    private var evaluations = mutableListOf<Evaluation>()
    private val evaluationReportAdapter = EvaluationReportAdapter()
    //是否是刷新患者列表
    private val isRefreshPatient = AtomicBoolean(false)
    //是否是加载更多患者列表
    private val isLoadMorePatient = AtomicBoolean(false)
    //患者列表页码，从1开始,0表示尚未加载数据
    private var patientPage = 0

    override fun createBinding(inflater: LayoutInflater, container: ViewGroup?): FragmentEvaluationManagementBinding {
        return FragmentEvaluationManagementBinding.inflate(inflater,container,false)
    }

    override fun initParam() {
    }

    override fun initView() {
        initListener()
        binding.rvEvaluation.adapter = evaluationReportAdapter
        binding.rvEvaluation.layoutManager =
            LinearLayoutManager(mActivity, LinearLayoutManager.VERTICAL, false)

        binding.smartRefresh.setRefreshHeader(ClassicsHeader(mActivity))
        binding.smartRefresh.setRefreshFooter(ClassicsFooter(mActivity))
        binding.smartRefresh.setEnableLoadMoreWhenContentNotFull(false)
    }

    override fun initObserver() {
        evaluationVM.evaluationListLiveData.observe(this){
            if (isRefreshPatient.get()){//刷新
                val list = it?.list.orEmpty()
                val total = it?.total?:0
                binding.smartRefresh.finishRefresh()
                evaluations.clear()
                evaluations.addAll(list)
                evaluationReportAdapter.submitList(evaluations)
                patientPage = 1
                if (evaluations.size >= total){
                    binding.smartRefresh.finishLoadMoreWithNoMoreData()
                }
                isRefreshPatient.set(false)
                binding.tvNoData.isVisible = evaluations.isEmpty()
            }else if (isLoadMorePatient.get()){//加载更多
                val list = it?.list
                val total = it?.total?:0
                if (!list.isNullOrEmpty()){
                    evaluations.addAll(list)
                    evaluationReportAdapter.addMoreItems(list)
                    patientPage++
                }
                if (evaluations.size >= total){
                    binding.smartRefresh.finishLoadMoreWithNoMoreData()
                }else{
                    binding.smartRefresh.finishLoadMore(true)
                }
                isLoadMorePatient.set(false)
            }else{//首次加载数据
                val list = it?.list.orEmpty()
                val total = it?.total?:0
                evaluations.clear()
                evaluations.addAll(list)
                evaluationReportAdapter.submitList(evaluations)
                patientPage = 1
                if (evaluations.size >= total){
                    binding.smartRefresh.finishLoadMoreWithNoMoreData()
                }
                binding.rvEvaluation.scrollToPosition(0)
                binding.tvNoData.isVisible = evaluations.isEmpty()
            }
        }
        evaluationVM.deleteEvaluationLiveData.observe(this){
            if (it) {
                val keywords = binding.etSearch.text.toString()
                loadEvaluation(1,keywords = keywords)
                Toast.makeText(mActivity,getString(R.string.str_evaluation_delete_successfully), Toast.LENGTH_SHORT).show()
            }else{
                Toast.makeText(mActivity,getString(R.string.str_evaluation_delete_failed), Toast.LENGTH_SHORT).show()
            }
        }
    }

    override fun initData() {
        loadEvaluation(1)
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (!hidden){
            binding.etSearch.setText("")
            loadEvaluation(1)
        }
    }

    private fun initListener() {
        binding.etSearch.setOnEditorActionListener { view, actionId, event ->
            Logger.d(TAG, msg = "etSearch actionId = $actionId")
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                val keywords = binding.etSearch.text.toString()
                Logger.d(TAG, msg = "etSearch ACTION_SEARCH keywords = $keywords")
                loadEvaluation(1,keywords = keywords)
                hideKeyboard(view)
                // 返回 true 表示事件已处理
                true
            } else {
                // 其他动作不处理
                false
            }
        }
        binding.ivCross.setOnSingleClickListener {
            binding.etSearch.setText("")
        }
        binding.ivSearchPatients.setOnSingleClickListener {
            val keywords = binding.etSearch.text.toString()
            Logger.d(TAG, msg = "ivSearchPatients keywords = $keywords")
            loadEvaluation(1,keywords = keywords)
            hideKeyboard(binding.etSearch)
        }
        binding.etSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                binding.ivCross.isVisible = s.toString().isNotEmpty()
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
        binding.smartRefresh.setOnRefreshListener{
            val keywords = binding.etSearch.text.toString()
            refreshEvaluation(1,keywords = keywords)
        }
        binding.smartRefresh.setOnLoadMoreListener {
            val keywords = binding.etSearch.text.toString()
            loadMoreEvaluation(patientPage + 1,keywords = keywords)
        }
        evaluationReportAdapter.onActionClick = {action,patient ->
            when(action){
                EvaluationReportAdapter.ACTION_VIEW -> {
                    patient.reportUrl?.let {
                        startActivity(EvaluationReportActivity.createIntent(mActivity,it))
                    }
                }
                EvaluationReportAdapter.ACTION_DELETE -> {
                    DeleteEvaluationDialog(mActivity).apply {
                        onOkClick = {
                            patient.id?.let {
                                evaluationVM.deleteEvaluation(listOf(it))
                            }
                        }
                    }.show()
                }
            }
        }
    }

    /**
     * 加载评估列表
     */
    private fun loadEvaluation(page:Int,size:Int = 10,keywords:String? = null){
        Logger.d(TAG, msg = "loadEvaluation page = $page,size = $size")
        evaluationVM.getEvaluationList(page, size,keywords = keywords)
    }

    /**
     * 下拉刷新评估列表
     */
    private fun refreshEvaluation(page:Int,size:Int = 10,keywords:String? = null){
        Logger.d(TAG, msg = "refreshEvaluation page = $page,size = $size")
        isRefreshPatient.set(true)
        evaluationVM.getEvaluationList(page, size,keywords = keywords)
    }

    /**
     * 上划加载更多评估列表
     */
    private fun loadMoreEvaluation(page:Int,size:Int = 10,keywords:String? = null){
        Logger.d(TAG, msg = "loadMoreEvaluation page = $page,size = $size")
        isLoadMorePatient.set(true)
        evaluationVM.getEvaluationList(page, size,keywords = keywords)
    }

    // 关闭键盘方法（优化版）
    private fun hideKeyboard(view: View) {
        val imm = view.context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(view.windowToken, 0)
    }

}