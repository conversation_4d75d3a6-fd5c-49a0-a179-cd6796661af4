<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/app_launcher_bg">

    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:src="@drawable/ic_main_logo"
        android:scaleType="fitStart"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@+id/cl_setting"
        app:layout_constraintBottom_toBottomOf="@+id/cl_setting"
        android:layout_marginStart="20dp"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_setting"
        android:layout_width="40dp"
        android:layout_height="40dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="25dp">

        <ImageView
            android:id="@+id/iv_setting"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/ic_menu" />

        <ImageView
            android:id="@+id/iv_setting_red_dot"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:src="@drawable/ic_red_dot"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:visibility="gone"
            tools:visibility="visible"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_product_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_product_name_value"
        android:textColor="@color/color_333333"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/et_account_number"
        app:layout_constraintVertical_chainStyle="packed"/>

    <EditText
        android:id="@+id/et_account_number"
        android:layout_width="400dp"
        android:layout_height="55dp"
        android:textSize="18sp"
        android:textColor="@color/color_333333"
        android:textColorHint="#ABADB0"
        android:hint="@string/str_please_enter_account_number"
        android:maxLines="1"
        android:maxLength="20"
        android:inputType="text"
        android:includeFontPadding="false"
        android:textCursorDrawable="@drawable/input_cursor"
        android:background="@drawable/login_input_bg"
        android:imeOptions="actionNext"
        android:singleLine="true"
        android:layout_marginTop="30dp"
        android:paddingStart="65dp"
        android:focusable="true"
        android:focusableInTouchMode="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_product_name"
        app:layout_constraintBottom_toTopOf="@+id/et_password" />

    <ImageView
        android:layout_width="17dp"
        android:layout_height="21dp"
        android:src="@drawable/icon_login_account_number"
        android:layout_marginStart="30dp"
        app:layout_constraintTop_toTopOf="@+id/et_account_number"
        app:layout_constraintBottom_toBottomOf="@+id/et_account_number"
        app:layout_constraintLeft_toLeftOf="@+id/et_account_number"/>

    <EditText
        android:id="@+id/et_password"
        android:layout_width="400dp"
        android:layout_height="55dp"
        android:textSize="18sp"
        android:textColor="@color/color_333333"
        android:textColorHint="#ABADB0"
        android:hint="@string/str_please_enter_password"
        android:maxLines="1"
        android:maxLength="20"
        android:includeFontPadding="false"
        android:textCursorDrawable="@drawable/input_cursor"
        android:background="@drawable/login_input_bg"
        android:imeOptions="actionDone"
        android:inputType="textPassword"
        android:singleLine="true"
        android:paddingStart="65dp"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:layout_marginTop="15dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/et_account_number"
        app:layout_constraintBottom_toTopOf="@+id/iv_remember_username_password"/>

    <ImageView
        android:layout_width="17dp"
        android:layout_height="21dp"
        android:src="@drawable/icon_login_password"
        android:layout_marginStart="30dp"
        app:layout_constraintTop_toTopOf="@+id/et_password"
        app:layout_constraintBottom_toBottomOf="@+id/et_password"
        app:layout_constraintLeft_toLeftOf="@+id/et_password"/>

    <ImageView
        android:id="@+id/iv_display_password"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:src="@drawable/selector_display_password"
        android:layout_marginEnd="20dp"
        app:layout_constraintTop_toTopOf="@+id/et_password"
        app:layout_constraintBottom_toBottomOf="@+id/et_password"
        app:layout_constraintRight_toRightOf="@+id/et_password"/>

    <ImageView
        android:id="@+id/iv_remember_username_password"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:src="@drawable/selector_remember_username_password"
        android:layout_marginStart="30dp"
        android:layout_marginTop="15dp"
        app:layout_constraintTop_toBottomOf="@+id/et_password"
        app:layout_constraintLeft_toLeftOf="@+id/et_password"
        app:layout_constraintBottom_toTopOf="@+id/tv_login"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_remember_username_password"
        android:textColor="@color/color_333333"
        android:textSize="14sp"
        android:layout_marginStart="5dp"
        app:layout_constraintTop_toTopOf="@+id/iv_remember_username_password"
        app:layout_constraintBottom_toBottomOf="@+id/iv_remember_username_password"
        app:layout_constraintLeft_toRightOf="@+id/iv_remember_username_password"/>

    <TextView
        android:id="@+id/tv_login"
        android:layout_width="400dp"
        android:layout_height="55dp"
        android:text="@string/str_login"
        android:textColor="@color/white"
        android:textSize="18sp"
        android:gravity="center"
        android:background="@drawable/common_eb4e89_round_bg"
        android:layout_marginTop="25dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_remember_username_password"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>