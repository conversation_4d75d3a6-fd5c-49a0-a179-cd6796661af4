package com.airdoc.ytrts.ppg.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.lepu.blepro.objs.Bluetooth

/**
 * FileName: PpgViewModel
 * Author by lilin,Date on 2025/6/18 10:56
 * PS: Not easy to write code, please indicate.
 */
class PpgViewModel : ViewModel() {

    //当前蓝牙设备
    val ppgDeviceLiveData = MutableLiveData<Bluetooth?>()
    //当前蓝牙设备连接状态
    val bleStateLiveData = MutableLiveData<Int>()

    fun setPpgDevice(bluetooth: Bluetooth){
        ppgDeviceLiveData.postValue(bluetooth)
    }

    fun getPpgDevice(): Bluetooth?{
        return ppgDeviceLiveData.value
    }

    fun getBleState(): Int?{
        return bleStateLiveData.value
    }

    fun setBleState(state: Int){
        bleStateLiveData.postValue(state)
    }

}