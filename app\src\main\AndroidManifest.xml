<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />

    <!-- 基本蓝牙权限 -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <!--位置权限 API 28 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <!-- 位置权限低版本 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <!-- 蓝牙扫描需申请 API 31-->
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN"  />
    <!-- 蓝牙链接，开关蓝牙需申请， API 31 -->
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <!-- 管理蓝牙设置 蓝牙扫描，蓝牙链接，开关蓝牙低版本申请 -->
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <!-- 应用程序在蓝牙低功耗（Bluetooth Low Energy, BLE）模式下广播广告数据。这个权限主要用于使设备能够作为广告者（advertiser）发送广播信号，以便其他设备可以发现它。-->
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    <!-- 如果应用必须安装在支持蓝牙的设备上，可以将下面的required的值设置为true。-->
    <uses-feature android:name="android.hardware.bluetooth_le"
        android:required="false"/>

    <application
        android:name=".YtRtsApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:theme="@style/Theme.App"
        tools:targetApi="31"
        android:networkSecurityConfig="@xml/network_security_config"
        android:usesCleartextTraffic="true"
        android:extractNativeLibs="true"
        android:requestLegacyExternalStorage="true"
        android:enableOnBackInvokedCallback="true">

        <meta-data
            android:name="design_width_in_dp"
            android:value="960"/>
        <meta-data
            android:name="design_height_in_dp"
            android:value="540"/>

        <activity
            android:name=".LauncherActivity"
            android:exported="true"
            android:theme="@style/LauncherActivityTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".home.MainActivity"
            android:launchMode="singleTask"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|navigation">
        </activity>

        <activity
            android:name=".user.LoginActivity"
            android:launchMode="singleTask"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|navigation">
        </activity>

        <activity
            android:name=".evaluation.EvaluationActivity"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|navigation">
        </activity>

        <activity
            android:name=".home.ProductInformationActivity"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|navigation">
        </activity>

        <activity
            android:name=".user.ChangePasswordActivity"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|navigation">
        </activity>

        <activity
            android:name=".home.evaluation.EvaluationReportActivity"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|navigation">
        </activity>

    </application>

</manifest>