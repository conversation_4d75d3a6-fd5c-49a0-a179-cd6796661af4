package com.airdoc.ytrts.home.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.airdoc.ytrts.home.api.PatientApiService
import com.airdoc.ytrts.home.bean.Patient
import com.airdoc.ytrts.home.bean.PatientAdd
import com.airdoc.ytrts.home.bean.PatientList
import com.airdoc.ytrts.net.MainRetrofitClient
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * FileName: PatientRepository
 * Author by lilin,Date on 2025/6/20 10:33
 * PS: Not easy to write code, please indicate.
 */
class PatientRepository : BaseRepository() {

    /**
     * 获取患者列表
     * @param page 页码,从1开始
     * @param size 每页条数,示例值(10)
     * @param sort 排序条件,示例值(按创建时间倒序：createTime,desc)
     * @param gender 性别{0=未知, 1=男, 2=女},可用值:0,1,2,示例值(1)
     * @param keywords 搜索关键词,示例值(张三)
     * @param authorization 授权token,示例值(Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c)
     */
    suspend fun getPatientList(page:Int,size:Int,sort:String?,
                               gender:Int?,keywords:String?,
                               authorization: String
    ): ApiResponse<PatientList> {
        return executeHttp {
            MainRetrofitClient.createService(PatientApiService::class.java).getPatientList(page, size, sort, gender, keywords,authorization)
        }
    }

    /**
     * 添加患者
     * @param name 姓名,示例值(张三)
     * @param gender 性别{0=未知, 1=男, 2=女},可用值:0,1,2,示例值(1)
     * @param birthday 生日,示例值(2023-04-23)
     * @param phoneNumber 手机号,示例值(13800000000)
     * @param authorization 授权token,示例值(Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQ)
     */
    suspend fun addPatient(name:String,gender:Int,birthday:String,
                           phoneNumber:String,authorization: String
    ): ApiResponse<PatientAdd> {
        return executeHttp {
            val hashMap = HashMap<String, Any>()
            hashMap["name"] = name
            hashMap["gender"] = gender
            hashMap["birthday"] = birthday
            hashMap["phoneNumber"] = phoneNumber
            val json = gson.toJson(hashMap)
            val requestBody =
                json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
            MainRetrofitClient.createService(PatientApiService::class.java).addPatient(authorization,requestBody)
        }
    }

    /**
     * 修改患者
     * @param id 患者ID,示例值(1)
     * @param name 姓名,示例值(张三)
     * @param gender 性别{0=未知, 1=男, 2=女},可用值:0,1,2,示例值(1)
     * @param birthday 生日,示例值(2023-04-23)
     * @param phoneNumber 手机号,示例值(13800000000)
     * @param authorization 授权token,示例值(Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQ)
     */
    suspend fun modifyPatient(id:Long, name:String, gender:Int, birthday:String,
                              phoneNumber:String, authorization: String
    ): ApiResponse<Any> {
        return executeHttp {
            val hashMap = HashMap<String, Any>()
            hashMap["name"] = name
            hashMap["gender"] = gender
            hashMap["birthday"] = birthday
            hashMap["phoneNumber"] = phoneNumber
            val json = gson.toJson(hashMap)
            val requestBody =
                json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
            MainRetrofitClient.createService(PatientApiService::class.java).modifyPatient(id,authorization,requestBody)
        }
    }

    /**
     * 查询患者信息
     * @param id 患者ID,示例值(1)
     * @param authorization 授权token,示例值(Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQ)
     */
    suspend fun queryPatient(id:Long,authorization: String): ApiResponse<Patient> {
        return executeHttp {
            MainRetrofitClient.createService(PatientApiService::class.java).queryPatient(id,authorization)
        }
    }

    /**
     * 查询患者信息
     * @param ids 患者ID列表,示例值[1,2]
     * @param authorization 授权token,示例值(Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQ)
     */
    suspend fun deletePatient(ids: List<Long>,authorization: String): ApiResponse<Any> {
        return executeHttp {
            val hashMap = HashMap<String, Any>()
            hashMap["ids"] = ids
            val json = gson.toJson(hashMap)
            val requestBody =
                json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
            MainRetrofitClient.createService(PatientApiService::class.java).deletePatient(requestBody,authorization)
        }
    }

}