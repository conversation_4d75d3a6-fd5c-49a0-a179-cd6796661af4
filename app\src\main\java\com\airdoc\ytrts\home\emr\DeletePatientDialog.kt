package com.airdoc.ytrts.home.emr

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import androidx.core.graphics.drawable.toDrawable
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.ytrts.databinding.DialogDeletePatientBinding

/**
 * FileName: DeletePatientDialog
 * Author by lilin,Date on 2025/6/20 11:20
 * PS: Not easy to write code, please indicate.
 * 删除患者
 */
class DeletePatientDialog(context: Context) : BaseCommonDialog(context) {

    companion object{
        private val TAG = DeletePatientDialog::class.java.simpleName
    }

    private lateinit var binding: DialogDeletePatientBinding
    var onOkClick:(() -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        binding = DialogDeletePatientBinding.inflate(LayoutInflater.from(context))
        setContentView(binding.root)
        window?.apply {
            val width = 400.dp2px(context)
            val height = 200.dp2px(context)
            setLayout(width, height)
        }
        setCancelable(false)
        setCanceledOnTouchOutside(false)

        initView()
        initListener()
    }

    private fun initView(){

    }

    private fun initListener(){
        binding.tvOk.setOnSingleClickListener {
            onOkClick?.invoke()
            dismiss()
        }
        binding.tvCancel.setOnSingleClickListener {
            dismiss()
        }
    }

}