package com.airdoc.ytrts.ppg.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: BandPowers
 * Author by lilin,Date on 2025/6/21 14:19
 * PS: Not easy to write code, please indicate.
 */
@Parcelize
data class BandPowers(
    val vlfPower: Double,
    val lfPower: Double,
    val hfPower: Double,
    val totalPower: Double,
    val lfHfRatio: Double,
    val stepPower: List<Double>
): Parcelable
