package com.airdoc.ytrts.home.evaluation

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.ytrts.R
import com.airdoc.ytrts.databinding.ItemEvaluationHeaderLayoutBinding
import com.airdoc.ytrts.databinding.ItemEvaluationReportLayoutBinding
import com.airdoc.ytrts.home.bean.Evaluation
import com.airdoc.ytrts.user.enumeration.Gender

/**
 * FileName: EvaluationReportAdapter
 * Author by lilin,Date on 2025/6/17 16:34
 * PS: Not easy to write code, please indicate.
 */
class EvaluationReportAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val TYPE_HEADER = 0
        private const val TYPE_ITEM = 1
        //查看
        const val ACTION_VIEW = "view"
        //删除
        const val ACTION_DELETE = "delete"
    }

    private val evaluations = mutableListOf<Evaluation>()
    var onActionClick: ((action: String, evaluation: Evaluation) -> Unit)? = null

    fun submitList(newList: List<Evaluation>) {
        evaluations.clear()
        evaluations.addAll(newList)
        notifyDataSetChanged()
    }

    fun addMoreItems(newItems: List<Evaluation>) {
        val startPos = evaluations.size + 1
        evaluations.addAll(newItems)
        notifyItemRangeInserted(startPos, newItems.size)
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): RecyclerView.ViewHolder {
        return if (viewType == TYPE_HEADER) {
            HeaderHolder(
                ItemEvaluationHeaderLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            )
        } else {
            EvaluationReportHolder(
                ItemEvaluationReportLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            )
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is HeaderHolder -> holder.bind()
            is EvaluationReportHolder -> {
                if (position - 1 in evaluations.indices){
                    holder.bind(evaluations[position - 1])
                }
            }
        }
    }

    override fun getItemCount(): Int {
        return evaluations.size + 1
    }

    override fun getItemViewType(position: Int): Int {
        return if (position == 0) TYPE_HEADER else TYPE_ITEM
    }

    inner class HeaderHolder(val binding: ItemEvaluationHeaderLayoutBinding) : RecyclerView.ViewHolder(binding.root){

        fun bind(){
            binding.apply {
                root.setBackgroundColor("#EFF3F6".toColorInt())
                tvReportId.textSize = 14f
                tvEvaluationDate.textSize = 14f
                tvName.textSize = 14f
                tvGender.textSize = 14f
                tvAge.textSize = 14f
                tvOperate.textSize = 14f
                tvReportId.text = root.context.getString(R.string.str_report_id)
                tvEvaluationDate.text = root.context.getString(R.string.str_evaluation_date)
                tvName.text = root.context.getString(R.string.str_name)
                tvGender.text = root.context.getString(R.string.str_gender)
                tvAge.text = root.context.getString(R.string.str_age)
                tvOperate.text = root.context.getString(R.string.str_operate)
            }
        }
    }

    inner class EvaluationReportHolder(val binding: ItemEvaluationReportLayoutBinding) : RecyclerView.ViewHolder(binding.root){

        fun bind(evaluation: Evaluation){
            binding.apply {
                root.background = null
                tvReportId.textSize = 14f
                tvEvaluationDate.textSize = 14f
                tvName.textSize = 14f
                tvGender.textSize = 14f
                tvAge.textSize = 14f
                tvView.textSize = 14f
                tvDelete.textSize = 14f
                tvReportId.text = evaluation.id.toString()
                tvEvaluationDate.text = evaluation.createTime
                tvName.text = evaluation.name
                tvGender.text = when(evaluation.gender){
                    Gender.FEMALE.num -> {
                        root.context.getString(R.string.str_female)
                    }
                    else -> {
                        root.context.getString(R.string.str_male)
                    }
                }
                tvAge.text = evaluation.age.toString()
                tvView.setOnSingleClickListener {
                    onActionClick?.invoke(ACTION_VIEW,evaluation)
                }
                tvDelete.setOnSingleClickListener {
                    onActionClick?.invoke(ACTION_DELETE,evaluation)
                }
            }
        }
    }

}