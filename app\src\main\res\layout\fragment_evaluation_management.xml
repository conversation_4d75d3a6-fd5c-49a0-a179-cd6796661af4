<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white">

    <EditText
        android:id="@+id/et_search"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:minWidth="228dp"
        android:textSize="12sp"
        android:textColor="@color/color_333333"
        android:textColorHint="#999999"
        android:hint="@string/str_search_report_id_file_id_name"
        android:maxLines="1"
        android:includeFontPadding="false"
        android:textCursorDrawable="@drawable/input_cursor"
        android:background="@drawable/search_patients_bg"
        android:imeOptions="actionSearch"
        android:singleLine="true"
        android:paddingStart="15dp"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:layout_marginTop="12dp"
        android:layout_marginStart="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <ImageView
        android:id="@+id/iv_search_patients"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:src="@drawable/ic_search_patients"
        android:layout_marginEnd="10dp"
        app:layout_constraintTop_toTopOf="@+id/et_search"
        app:layout_constraintBottom_toBottomOf="@+id/et_search"
        app:layout_constraintRight_toRightOf="@+id/et_search"/>

    <ImageView
        android:id="@+id/iv_cross"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:src="@drawable/ic_cross"
        android:layout_marginEnd="5dp"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintTop_toTopOf="@+id/iv_search_patients"
        app:layout_constraintBottom_toBottomOf="@+id/iv_search_patients"
        app:layout_constraintRight_toLeftOf="@+id/iv_search_patients"/>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/smart_refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@+id/et_search"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_evaluation"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="15dp"/>

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <TextView
        android:id="@+id/tv_no_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_no_data"
        android:textColor="@color/color_333333"
        android:textSize="18sp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@+id/smart_refresh"
        app:layout_constraintBottom_toBottomOf="@+id/smart_refresh"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>


</androidx.constraintlayout.widget.ConstraintLayout>