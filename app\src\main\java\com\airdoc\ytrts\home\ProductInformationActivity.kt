package com.airdoc.ytrts.home

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.airdoc.component.common.base.BaseCommonActivity
import com.airdoc.component.common.base.BaseCommonApplication
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.utils.PackageUtils
import com.airdoc.ytrts.databinding.ActivityProductInformationBinding

/**
 * FileName: ProductInformationActivity
 * Author by lilin,Date on 2025/6/18 19:57
 * PS: Not easy to write code, please indicate.
 * 产品信息页面
 */
class ProductInformationActivity : BaseCommonActivity(){

    companion object{
        private val TAG = ProductInformationActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            val intent = Intent(context, ProductInformationActivity::class.java)
            return intent
        }
    }

    private lateinit var binding: ActivityProductInformationBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityProductInformationBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initView()
    }

    private fun initView() {
//        binding.tvVersionValue.text = "V${PackageUtils.getVersionName(BaseCommonApplication.instance, BaseCommonApplication.instance.packageName)}"
        binding.ivBack.setOnSingleClickListener {
            finish()
        }
    }

}