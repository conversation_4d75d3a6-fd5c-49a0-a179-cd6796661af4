package com.airdoc.ytrts

import android.os.Bundle
import com.airdoc.component.common.base.BaseCommonActivity
import com.airdoc.ytrts.home.MainActivity
import com.airdoc.ytrts.user.LoginActivity
import com.airdoc.ytrts.user.UserManager

/**
 * FileName: LauncherActivity
 * Author by lilin,Date on 2025/6/16 14:18
 * PS: Not easy to write code, please indicate.
 */
class LauncherActivity : BaseCommonActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if (UserManager.isLogin()){
            startActivity(MainActivity.createIntent(this))
        }else{
            startActivity(LoginActivity.createIntent(this))
        }
        finish()
    }
}