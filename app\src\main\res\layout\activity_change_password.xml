<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="#EFF3F6"
    android:id="@+id/cl_root">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/icon_back_black_coarse"
        android:layout_marginTop="20dp"
        android:layout_marginStart="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_change_password"
        android:textColor="@color/color_333333"
        android:textSize="18sp"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:id="@+id/tv_original_password"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:text="@string/str_original_password"
        android:textColor="@color/color_333333"
        android:textSize="16sp"
        android:gravity="center_vertical|end"
        android:layout_marginStart="100dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/et_original_password"
        app:layout_constraintBottom_toTopOf="@+id/tv_new_password"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintVertical_chainStyle="packed"/>

    <EditText
        android:id="@+id/et_original_password"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:textSize="16sp"
        android:textColor="@color/color_333333"
        android:maxLines="1"
        android:maxLength="20"
        android:includeFontPadding="false"
        android:textCursorDrawable="@drawable/input_cursor"
        android:background="@drawable/new_user_input_bg"
        android:imeOptions="actionNext"
        android:singleLine="true"
        android:paddingStart="30dp"
        android:focusable="true"
        android:inputType="textPassword"
        android:focusableInTouchMode="true"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="100dp"
        app:layout_constraintTop_toTopOf="@+id/tv_original_password"
        app:layout_constraintBottom_toBottomOf="@+id/tv_original_password"
        app:layout_constraintLeft_toRightOf="@+id/tv_original_password"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintHorizontal_weight="4"/>

    <ImageView
        android:id="@+id/iv_display_password"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:src="@drawable/selector_display_password"
        android:layout_marginEnd="20dp"
        app:layout_constraintTop_toTopOf="@+id/et_original_password"
        app:layout_constraintBottom_toBottomOf="@+id/et_original_password"
        app:layout_constraintRight_toRightOf="@+id/et_original_password"/>

    <TextView
        android:id="@+id/tv_new_password"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:text="@string/str_new_password"
        android:textColor="@color/color_333333"
        android:textSize="16sp"
        android:gravity="center_vertical|end"
        android:layout_marginStart="100dp"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_original_password"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/et_new_password"
        app:layout_constraintBottom_toTopOf="@+id/tv_confirm_password"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintHorizontal_weight="1"/>

    <EditText
        android:id="@+id/et_new_password"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:textSize="16sp"
        android:textColor="@color/color_333333"
        android:hint=""
        android:maxLines="1"
        android:maxLength="20"
        android:includeFontPadding="false"
        android:textCursorDrawable="@drawable/input_cursor"
        android:background="@drawable/new_user_input_bg"
        android:imeOptions="actionNext"
        android:singleLine="true"
        android:paddingStart="30dp"
        android:focusable="true"
        android:inputType="textPassword"
        android:focusableInTouchMode="true"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="100dp"
        app:layout_constraintTop_toTopOf="@+id/tv_new_password"
        app:layout_constraintBottom_toBottomOf="@+id/tv_new_password"
        app:layout_constraintLeft_toRightOf="@+id/tv_new_password"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintHorizontal_weight="4"/>

    <TextView
        android:id="@+id/tv_confirm_password"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:text="@string/str_confirm_password"
        android:textColor="@color/color_333333"
        android:textSize="16sp"
        android:gravity="center_vertical|end"
        android:layout_marginStart="100dp"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_new_password"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/et_confirm_password"
        app:layout_constraintBottom_toTopOf="@+id/tv_ok"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintHorizontal_weight="1"/>

    <EditText
        android:id="@+id/et_confirm_password"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:textSize="16sp"
        android:textColor="@color/color_333333"
        android:maxLines="1"
        android:maxLength="20"
        android:includeFontPadding="false"
        android:textCursorDrawable="@drawable/input_cursor"
        android:background="@drawable/new_user_input_bg"
        android:imeOptions="actionNext"
        android:singleLine="true"
        android:paddingStart="30dp"
        android:focusable="true"
        android:inputType="textPassword"
        android:focusableInTouchMode="true"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="100dp"
        app:layout_constraintTop_toTopOf="@+id/tv_confirm_password"
        app:layout_constraintBottom_toBottomOf="@+id/tv_confirm_password"
        app:layout_constraintLeft_toRightOf="@+id/tv_confirm_password"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintHorizontal_weight="4"/>

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_cancel"
        android:textColor="#8D9EAC"
        android:textSize="17sp"
        android:background="@drawable/common_d6dce1_round_bg"
        android:gravity="center"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_ok"
        app:layout_constraintBottom_toBottomOf="@+id/tv_ok"
        app:layout_constraintRight_toLeftOf="@+id/tv_ok"
        app:layout_constraintHorizontal_chainStyle="packed"/>

    <TextView
        android:id="@+id/tv_ok"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_ok"
        android:textColor="@color/white"
        android:textSize="17sp"
        android:background="@drawable/common_eb4e89_round_bg"
        android:gravity="center"
        android:layout_marginStart="20dp"
        android:layout_marginTop="100dp"
        app:layout_constraintLeft_toRightOf="@+id/tv_cancel"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_confirm_password"/>

    <TextView
        android:id="@+id/tv_password_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_password_tips"
        android:textSize="12sp"
        android:textColor="@color/color_666666"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="20dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>