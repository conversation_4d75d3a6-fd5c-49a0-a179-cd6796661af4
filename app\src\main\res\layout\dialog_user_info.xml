<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="450dp"
    android:layout_height="395dp"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/common_eff3f6_round_15_bg"
    android:id="@+id/cl_root">

    <ImageView
        android:id="@+id/iv_avatar"
        android:layout_width="88dp"
        android:layout_height="88dp"
        tools:src="@drawable/ic_male_avatar_round"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/et_name"
        app:layout_constraintVertical_chainStyle="packed"/>

    <EditText
        android:id="@+id/et_name"
        android:layout_width="300dp"
        android:layout_height="40dp"
        android:textSize="16sp"
        android:textColor="@color/color_333333"
        android:maxLines="1"
        android:includeFontPadding="false"
        android:textCursorDrawable="@drawable/input_cursor"
        android:background="@drawable/new_user_input_bg"
        android:imeOptions="actionNext"
        android:singleLine="true"
        android:paddingStart="30dp"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:layout_marginTop="20dp"
        android:layout_marginStart="88dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_avatar"
        app:layout_constraintBottom_toTopOf="@+id/rg_gender"/>

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_name"
        android:textColor="@color/color_333333"
        android:textSize="16sp"
        android:layout_marginEnd="10dp"
        app:layout_constraintTop_toTopOf="@+id/et_name"
        app:layout_constraintBottom_toBottomOf="@+id/et_name"
        app:layout_constraintRight_toLeftOf="@+id/et_name"/>

    <RadioGroup
        android:id="@+id/rg_gender"
        android:layout_width="300dp"
        android:layout_height="40dp"
        android:orientation="horizontal"
        android:layout_marginStart="88dp"
        android:layout_marginTop="8dp"
        android:paddingStart="30dp"
        android:background="@drawable/new_user_input_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/et_name"
        app:layout_constraintBottom_toTopOf="@+id/et_phone">

        <RadioButton
            android:id="@+id/rb_male"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:checked="true"
            android:text="@string/str_male"
            android:button="@drawable/selector_common_radio_button"
            android:buttonTint="@color/selector_common_radio_button_tint"
            app:buttonTint="@color/selector_common_radio_button_tint"
            android:paddingStart="9dp"
            android:textSize="16sp"
            android:textColor="@color/selector_common_text_color"/>

        <RadioButton
            android:id="@+id/rb_female"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_female"
            android:button="@drawable/selector_common_radio_button"
            android:buttonTint="@color/selector_common_radio_button_tint"
            app:buttonTint="@color/selector_common_radio_button_tint"
            android:paddingStart="9dp"
            android:textSize="16sp"
            android:textColor="@color/selector_common_text_color"
            android:layout_marginStart="10dp"/>

    </RadioGroup>

    <TextView
        android:id="@+id/tv_gender"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_gender"
        android:textColor="@color/color_333333"
        android:textSize="16sp"
        android:layout_marginEnd="10dp"
        app:layout_constraintTop_toTopOf="@+id/rg_gender"
        app:layout_constraintBottom_toBottomOf="@+id/rg_gender"
        app:layout_constraintRight_toLeftOf="@+id/rg_gender"/>

    <EditText
        android:id="@+id/et_phone"
        android:layout_width="300dp"
        android:layout_height="40dp"
        android:textSize="16sp"
        android:textColor="@color/color_333333"
        android:maxLines="1"
        android:maxLength="15"
        android:includeFontPadding="false"
        android:textCursorDrawable="@drawable/input_cursor"
        android:background="@drawable/new_user_input_bg"
        android:imeOptions="actionNext"
        android:singleLine="true"
        android:inputType="phone"
        android:paddingStart="30dp"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:layout_marginTop="8dp"
        android:layout_marginStart="88dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rg_gender"
        app:layout_constraintBottom_toTopOf="@+id/tv_ok"/>

    <TextView
        android:id="@+id/tv_phone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_phone"
        android:textColor="@color/color_333333"
        android:textSize="16sp"
        android:layout_marginEnd="10dp"
        app:layout_constraintTop_toTopOf="@+id/et_phone"
        app:layout_constraintBottom_toBottomOf="@+id/et_phone"
        app:layout_constraintRight_toLeftOf="@+id/et_phone"/>

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_cancel"
        android:textColor="#8D9EAC"
        android:textSize="17sp"
        android:background="@drawable/common_d6dce1_round_bg"
        android:gravity="center"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_ok"
        app:layout_constraintBottom_toBottomOf="@+id/tv_ok"
        app:layout_constraintRight_toLeftOf="@+id/tv_ok"
        app:layout_constraintHorizontal_chainStyle="packed"/>

    <TextView
        android:id="@+id/tv_ok"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_ok"
        android:textColor="@color/white"
        android:textSize="17sp"
        android:background="@drawable/common_eb4e89_round_bg"
        android:gravity="center"
        android:layout_marginTop="20dp"
        android:layout_marginStart="20dp"
        app:layout_constraintLeft_toRightOf="@+id/tv_cancel"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/et_phone"/>

</androidx.constraintlayout.widget.ConstraintLayout>