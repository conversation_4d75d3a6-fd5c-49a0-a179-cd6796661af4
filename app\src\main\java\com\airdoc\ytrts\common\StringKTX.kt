package com.airdoc.ytrts.common

/**
 * FileName: StringKTX
 * Author by lilin,Date on 2025/6/19 16:15
 * PS: Not easy to write code, please indicate.
 */

/**
 * 检查字符串是否包含字母
 */
fun String.hasLetter(): Boolean = any { it.isLetter() }

/**
 * 检查字符串是否包含数字
 */
fun String.hasDigit(): Boolean = any { it.isDigit() }

/**
 * 检查是否只包含有效字符
 */
fun String.hasValidChars(): Boolean = all {
    it.isLetterOrDigit() || "!@#$%^&*()_+-=[]{};':\",.<>/?".contains(it)
}