package com.airdoc.ytrts.evaluation

import android.app.ProgressDialog
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.viewModels
import androidx.fragment.app.commit
import com.airdoc.component.common.base.BaseCommonActivity
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.TimeUtils
import com.airdoc.ytrts.R
import com.airdoc.ytrts.databinding.ActivityEvaluationBinding
import com.airdoc.ytrts.ppg.vm.PpgViewModel
import com.airdoc.ytrts.home.bean.Patient
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lepu.blepro.event.EventMsgConst
import com.lepu.blepro.ext.BleServiceHelper
import com.lepu.blepro.objs.Bluetooth
import com.lepu.blepro.objs.BluetoothController
import com.lepu.blepro.observer.BIOL
import com.lepu.blepro.observer.BleChangeObserver
import no.nordicsemi.android.ble.observer.ConnectionObserver
import java.io.File
import kotlin.getValue

/**
 * FileName: EvaluationActivity
 * Author by lilin,Date on 2025/6/17 19:57
 * PS: Not easy to write code, please indicate.
 * 评估页面
 */
class EvaluationActivity : BaseCommonActivity(),BleChangeObserver {

    companion object{
        private val TAG = EvaluationActivity::class.java.simpleName
        const val INPUT_PARAM_PATIENT = "patient"

        fun createIntent(context: Context,patient: Patient): Intent {
            val intent = Intent(context, EvaluationActivity::class.java)
            intent.putExtra(INPUT_PARAM_PATIENT, patient)
            return intent
        }
    }

    private lateinit var binding: ActivityEvaluationBinding
    private val models = intArrayOf(Bluetooth.MODEL_PC60FW)
    private val ppgVM by viewModels<PpgViewModel>()
    private lateinit var dialog: ProgressDialog
    private var mPatient: Patient? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityEvaluationBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initParam()
        initView()
        initData()
        initObserver()
    }

    private fun initParam(){
        mPatient = intent.getParcelableExtra(INPUT_PARAM_PATIENT)
    }

    private fun initView() {
        dialog = ProgressDialog(this)
        initListener()
        showEvaluationInit()
        binding.tvId.text = getString(R.string.str_id_s,mPatient?.id.toString())
        binding.tvName.text = getString(R.string.str_name_s,mPatient?.name)
        binding.tvAge.text = getString(R.string.str_age_d,mPatient?.age)
        binding.tvEvaluationDate.text = getString(R.string.str_evaluation_date_s,
            TimeUtils.parseTimeToTimeString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"))
    }

    private fun initData(){

    }

    private fun initObserver(){
        LiveEventBus.get<Int>(EventMsgConst.Ble.EventBleDeviceDisconnectReason).observe(this) {
            Logger.d(TAG, msg = "EventBleDeviceDisconnectReason = $it")
            // ConnectionObserver.REASON_NOT_SUPPORTED: SDK will not auto reconnect device, services error, try to reboot device
            val reason = when (it) {
                ConnectionObserver.REASON_UNKNOWN -> "The reason of disconnection is unknown."
                ConnectionObserver.REASON_SUCCESS -> "The disconnection was initiated by the user."
                ConnectionObserver.REASON_TERMINATE_LOCAL_HOST -> "The local device initiated disconnection."
                ConnectionObserver.REASON_TERMINATE_PEER_USER -> "The remote device initiated graceful disconnection."
                ConnectionObserver.REASON_LINK_LOSS -> "This reason will only be reported when ConnectRequest.shouldAutoConnect() was called and connection to the device was lost. Android will try to connect automatically."
                ConnectionObserver.REASON_NOT_SUPPORTED -> "The device does not hav required services."
                ConnectionObserver.REASON_TIMEOUT -> "The connection timed out. The device might have reboot, is out of range, turned off or doesn't respond for another reason."
                else -> "disconnect"
            }
            if (this::dialog.isInitialized) {
                dialog.dismiss()
            }
        }
        LiveEventBus.get<Int>(EventMsgConst.Ble.EventBleDeviceReady).observe(this){
            Logger.d(TAG, msg = "EventBleDeviceReady it = $it")
            if (this::dialog.isInitialized) {
                dialog.dismiss()
            }
            when(it){
                Bluetooth.MODEL_PC60FW ->{
                    showEvaluating()
                }
            }
        }

    }

    private fun initListener() {
        binding.ivBack.setOnSingleClickListener {
            finish()
        }
    }

    private fun showEvaluationInit(){
        supportFragmentManager.commit {
            val fragment = supportFragmentManager.findFragmentByTag(EvaluationInitFragment.FRAGMENT_TAG)
            if (fragment == null){
                replace(
                    R.id.fl_evaluation_container, EvaluationInitFragment.newInstance(),
                    EvaluationInitFragment.FRAGMENT_TAG)
            }
        }
    }

    private fun showEvaluating(){
        supportFragmentManager.commit {
            val fragment = supportFragmentManager.findFragmentByTag(EvaluatingFragment.FRAGMENT_TAG)
            if (fragment == null){
                replace(
                    R.id.fl_evaluation_container, EvaluatingFragment.newInstance(mPatient?.id?:0L),
                    EvaluatingFragment.FRAGMENT_TAG)
            }
        }
    }

    fun connectPpgDevice(bluetooth: Bluetooth){
        if (this::dialog.isInitialized) {
            dialog.show()
        }
        // set interface before connect
        BleServiceHelper.Companion.BleServiceHelper.setInterfaces(bluetooth.model)
        // add observer(ble state)
        lifecycle.addObserver(BIOL(this, intArrayOf(bluetooth.model)))
        // stop scan before connect
        BleServiceHelper.Companion.BleServiceHelper.stopScan()
        // connect
        BleServiceHelper.Companion.BleServiceHelper.connect(this, bluetooth.model, bluetooth.device)
        BluetoothController.clear()
    }

    fun startScan(){
        BleServiceHelper.Companion.BleServiceHelper.startScan(models)
    }

    override fun onBleStateChanged(model: Int, state: Int) {
        ppgVM.setBleState(state)
    }

    /**
     * 断开BLE服务
     */
    private fun disconnectBleService(){
        BleServiceHelper.Companion.BleServiceHelper.disconnect(false)
    }

    override fun onDestroy() {
        Log.d(TAG, "onDestroy")
        disconnectBleService()
        super.onDestroy()
    }

}