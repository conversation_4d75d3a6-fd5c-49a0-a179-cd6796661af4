# Lepu设备集成指南

## 📋 概述

本文档详细说明如何在Android项目中集成Lepu血氧仪设备，实现设备连接、数据采集和PPG信号分析功能。

## 🛠️ 环境要求

- **Android SDK**: API 21+
- **Kotlin**: 1.8+
- **目标设备**: 支持BLE的Android设备
- **Lepu SDK**: lepu-blepro-1.0.8.aar

## 📦 第一步：依赖配置

### 1.1 添加AAR库

将 `lepu-blepro-1.0.8.aar` 文件放入 `app/libs/` 目录

### 1.2 配置build.gradle.kts

```kotlin
dependencies {
    // Lepu核心库
    implementation(files("libs/lepu-blepro-1.0.8.aar"))
    
    // 必需依赖
    implementation("com.jeremyliao:live-event-bus-x:1.8.0")
    implementation("cn.wandersnail:ble:2.6.2")
    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7")
    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.8.7")
    implementation("com.google.code.gson:gson:2.11.0")
    
    // 数学计算库（用于PPG分析）
    implementation("org.apache.commons:commons-math3:3.6.1")
}
```

### 1.3 配置ProGuard规则

在 `proguard-rules.pro` 中添加：

```proguard
##---------------Begin: LePu血氧 ----------
-keep class com.lepu.blepro.ext.**{*;}
-keep class com.lepu.blepro.constants.**{*;}
-keep class com.lepu.blepro.event.**{*;}
-keep class com.lepu.blepro.objs.**{*;}
-keep class com.lepu.blepro.utils.DateUtil{*;}
-keep class com.lepu.blepro.utils.HexString{*;}
-keep class com.lepu.blepro.utils.StringUtilsKt{*;}
-keep class com.lepu.blepro.utils.DecompressUtil{*;}
-keep class com.lepu.blepro.utils.FilterUtil{*;}
-keep class com.lepu.blepro.observer.**{*;}
##---------------End: LePu血氧 ----------
```

## 🔐 第二步：权限配置

### 2.1 AndroidManifest.xml权限声明

```xml
<!-- 基本蓝牙权限 -->
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />

<!-- 位置权限（BLE扫描需要） -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

<!-- Android 12+ 蓝牙权限 -->
<uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
<uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
```

### 2.2 运行时权限申请

```kotlin
private fun requestPermissions() {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
        // Android 12+
        PermissionX.init(this)
            .permissions(
                Manifest.permission.BLUETOOTH_SCAN,
                Manifest.permission.BLUETOOTH_CONNECT,
                Manifest.permission.BLUETOOTH_ADVERTISE,
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION
            )
            .request { allGranted, _, _ ->
                if (allGranted) {
                    checkBluetooth()
                }
            }
    } else {
        // Android 11及以下
        PermissionX.init(this)
            .permissions(
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION
            )
            .request { allGranted, _, _ ->
                if (allGranted) {
                    checkBluetooth()
                }
            }
    }
}
```

## 🔧 第三步：核心代码集成

### 3.1 创建PPG数据模型

```kotlin
// PPGDataPoint.kt
@Parcelize
data class PPGDataPoint(
    val ppgValue: Double,      // PPG信号值
    val timestamp: Long        // 时间戳（纳秒）
) : Parcelable

// AnalysisResult.kt
data class AnalysisResult(
    val timeDomain: TimeDomainParameters,
    val frequencyDomain: FrequencyDomainParameters,
    val rrIntervals: List<Double>,
    val validIntervals: Int,
    val totalIntervals: Int
)

data class TimeDomainParameters(
    val meanRR: Double,        // 平均RR间期
    val sdnn: Double,          // RR间期标准差
    val rmssd: Double,         // 相邻RR间期差值的均方根
    val pnn50: Double,         // 相邻RR间期差值>50ms的百分比
    val heartRate: Double      // 心率
)

data class FrequencyDomainParameters(
    val totalPower: Double,    // 总功率
    val vlfPower: Double,      // 极低频功率
    val lfPower: Double,       // 低频功率
    val hfPower: Double,       // 高频功率
    val lfHfRatio: Double,     // LF/HF比值
    val stepPower: List<Double> // 分段功率
)
```

### 3.2 创建设备管理ViewModel

```kotlin
// PpgViewModel.kt
class PpgViewModel : ViewModel() {
    
    // 当前蓝牙设备
    val ppgDeviceLiveData = MutableLiveData<Bluetooth?>()
    
    // 当前蓝牙设备连接状态
    val bleStateLiveData = MutableLiveData<Int>()
    
    fun setPpgDevice(bluetooth: Bluetooth) {
        ppgDeviceLiveData.postValue(bluetooth)
    }
    
    fun getPpgDevice(): Bluetooth? {
        return ppgDeviceLiveData.value
    }
    
    fun getBleState(): Int? {
        return bleStateLiveData.value
    }
    
    fun setBleState(state: Int) {
        bleStateLiveData.postValue(state)
    }
}
```

## 🚀 第四步：初始化Lepu服务

### 4.1 在Application或MainActivity中初始化

```kotlin
class MainActivity : AppCompatActivity(), BleChangeObserver {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 初始化Lepu服务
        initLepuService()
        
        // 请求权限
        requestPermissions()
    }
    
    private fun initLepuService() {
        val checkService = BleServiceHelper.BleServiceHelper.checkService()
        if (!checkService) {
            // 配置原始数据保存路径
            val rawFolders = SparseArray<String>()
            rawFolders.set(Bluetooth.MODEL_ER1, "${getExternalFilesDir(null)?.absolutePath}/er1")
            rawFolders.set(Bluetooth.MODEL_PC60FW, "${getExternalFilesDir(null)?.absolutePath}/pc60fw")
            
            // 初始化服务（注意顺序）
            BleServiceHelper.BleServiceHelper
                .initRawFolder(rawFolders)
                .initService(application)
                .initLog(false)
        }
    }
}
```

## 🔍 第五步：设备扫描与连接

### 5.1 设备扫描

```kotlin
class DeviceScanActivity : AppCompatActivity() {
    
    private val supportedModels = intArrayOf(Bluetooth.MODEL_PC60FW)
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        initObserver()
        startScan()
    }
    
    private fun initObserver() {
        // 监听服务初始化完成
        LiveEventBus.get<Boolean>(EventMsgConst.Ble.EventServiceConnectedAndInterfaceInit)
            .observeSticky(this) {
                startScan()
            }
        
        // 监听设备发现
        LiveEventBus.get<Bluetooth>(EventMsgConst.Discovery.EventDeviceFound)
            .observe(this) { bluetooth ->
                // 处理发现的设备
                handleDeviceFound(bluetooth)
            }
    }
    
    private fun startScan() {
        BleServiceHelper.BleServiceHelper.startScan(supportedModels)
    }
    
    private fun handleDeviceFound(bluetooth: Bluetooth) {
        // 更新设备列表UI
        val devices = BluetoothController.getDevices()
        updateDeviceList(devices)
    }
}
```

### 5.2 设备连接

```kotlin
class DeviceConnectionManager : BleChangeObserver {

    fun connectDevice(bluetooth: Bluetooth) {
        // 1. 设置设备接口
        BleServiceHelper.BleServiceHelper.setInterfaces(bluetooth.model)

        // 2. 添加状态观察者
        lifecycle.addObserver(BIOL(this, intArrayOf(bluetooth.model)))

        // 3. 停止扫描
        BleServiceHelper.BleServiceHelper.stopScan()

        // 4. 连接设备
        BleServiceHelper.BleServiceHelper.connect(context, bluetooth.model, bluetooth.device)

        // 5. 清空设备控制器
        BluetoothController.clear()
    }

    override fun onBleStateChanged(model: Int, state: Int) {
        when (state) {
            Ble.State.CONNECTED -> {
                // 设备连接成功
                handleDeviceConnected(model)
            }
            Ble.State.DISCONNECTED -> {
                // 设备断开连接
                handleDeviceDisconnected(model)
            }
        }
    }

    private fun handleDeviceConnected(model: Int) {
        when (model) {
            Bluetooth.MODEL_PC60FW -> {
                // PC60FW设备连接成功，可以开始数据采集
                startDataCollection()
            }
        }
    }

    private fun disconnectDevice() {
        BleServiceHelper.BleServiceHelper.disconnect(false)
    }
}
```

## 📊 第六步：数据采集与处理

### 6.1 实时数据接收

```kotlin
class DataCollectionActivity : AppCompatActivity() {

    private val ppgDataPoints = mutableListOf<PPGDataPoint>()
    private val gson = Gson()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        initDataObserver()
    }

    private fun initDataObserver() {
        // 监听实时参数数据
        LiveEventBus.get<InterfaceEvent>(InterfaceEvent.PC60Fw.EventPC60FwRtParam)
            .observe(this) { event ->
                val data = event.data
                if (data is RtParam) {
                    // 处理实时参数：血氧、心率、灌注指数
                    handleRealTimeParams(data)
                }
            }

        // 监听实时波形数据
        LiveEventBus.get<InterfaceEvent>(InterfaceEvent.PC60Fw.EventPC60FwRtWave)
            .observe(this) { event ->
                val data = event.data
                if (data is RtWave) {
                    // 处理PPG波形数据
                    handlePpgWaveData(data)
                }
            }

        // 监听电池电量
        LiveEventBus.get<InterfaceEvent>(InterfaceEvent.PC60Fw.EventPC60FwBatLevel)
            .observe(this) { event ->
                val batteryLevel = event.data
                handleBatteryLevel(batteryLevel)
            }

        // 监听设备工作状态
        LiveEventBus.get<InterfaceEvent>(InterfaceEvent.PC60Fw.EventPC60FwWorkingStatus)
            .observe(this) { event ->
                val data = event.data
                if (data is WorkingStatus) {
                    handleWorkingStatus(data)
                }
            }
    }

    private fun handleRealTimeParams(rtParam: RtParam) {
        // 更新UI显示
        runOnUiThread {
            binding.tvSpo2.text = "SpO2: ${rtParam.spo2}%"
            binding.tvHeartRate.text = "HR: ${rtParam.pr} bpm"
            binding.tvPi.text = "PI: ${rtParam.pi}%"
        }
    }

    private fun handlePpgWaveData(rtWave: RtWave) {
        val waveData = rtWave.waveIntData.toList()

        waveData.forEachIndexed { index, value ->
            val timestamp = if (ppgDataPoints.isEmpty()) {
                System.currentTimeMillis() * 1_000_000L + System.nanoTime() % 1_000_000L
            } else {
                // 间隔20毫秒，转换为纳秒
                ppgDataPoints.last().timestamp + 20 * 1_000_000L
            }

            ppgDataPoints.add(PPGDataPoint(value.toDouble(), timestamp))
        }

        // 更新波形显示
        updateWaveformDisplay(waveData)
    }
}
```

### 6.2 PPG数据分析

将完整的PPGManager.kt复制到项目中，然后使用：

```kotlin
class PPGAnalyzer {

    fun analyzePpgData(ppgDataPoints: List<PPGDataPoint>): AnalysisResult? {
        return try {
            // 使用PPGManager进行完整分析
            PPGManager.analyzeECG(ppgDataPoints)
        } catch (e: Exception) {
            Log.e("PPGAnalyzer", "分析失败: ${e.message}")
            null
        }
    }

    fun saveAnalysisResult(result: AnalysisResult, context: Context) {
        val gson = Gson()
        val jsonResult = gson.toJson(result)

        // 创建保存目录
        val folder = PPGManager.createTimestampedFolder(context, "ppg_analysis")
        folder?.let {
            // 保存分析结果
            PPGManager.createFormattedJsonFile(jsonResult, it, "analysis_result")
        }
    }
}
```

## 🎨 第七步：UI集成示例

### 7.1 设备列表适配器

```kotlin
class DeviceListAdapter : RecyclerView.Adapter<DeviceListAdapter.DeviceViewHolder>() {

    private var devices = listOf<Bluetooth>()
    var onDeviceClick: ((Bluetooth) -> Unit)? = null

    fun updateDevices(newDevices: List<Bluetooth>) {
        devices = newDevices
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DeviceViewHolder {
        val binding = ItemDeviceBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return DeviceViewHolder(binding)
    }

    override fun onBindViewHolder(holder: DeviceViewHolder, position: Int) {
        holder.bind(devices[position])
    }

    override fun getItemCount() = devices.size

    inner class DeviceViewHolder(private val binding: ItemDeviceBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(device: Bluetooth) {
            binding.apply {
                tvDeviceName.text = device.name ?: "Unknown Device"
                tvDeviceAddress.text = device.macAddr

                root.setOnClickListener {
                    onDeviceClick?.invoke(device)
                }
            }
        }
    }
}
```

### 7.2 实时波形显示View

```kotlin
class PPGWaveView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val paint = Paint().apply {
        color = Color.GREEN
        strokeWidth = 2f
        style = Paint.Style.STROKE
        isAntiAlias = true
    }

    private val path = Path()
    private val dataPoints = mutableListOf<Int>()
    private val maxPoints = 500

    fun addDataPoints(newPoints: List<Int>) {
        dataPoints.addAll(newPoints)

        // 保持数据点数量在限制内
        while (dataPoints.size > maxPoints) {
            dataPoints.removeAt(0)
        }

        invalidate()
    }

    override fun onDraw(canvas: Canvas?) {
        super.onDraw(canvas)
        canvas ?: return

        if (dataPoints.isEmpty()) return

        path.reset()

        val width = width.toFloat()
        val height = height.toFloat()
        val pointSpacing = width / maxPoints

        // 计算数据范围
        val minValue = dataPoints.minOrNull()?.toFloat() ?: 0f
        val maxValue = dataPoints.maxOrNull()?.toFloat() ?: 1f
        val range = maxValue - minValue

        dataPoints.forEachIndexed { index, value ->
            val x = index * pointSpacing
            val normalizedValue = if (range > 0) (value - minValue) / range else 0.5f
            val y = height * (1 - normalizedValue)

            if (index == 0) {
                path.moveTo(x, y)
            } else {
                path.lineTo(x, y)
            }
        }

        canvas.drawPath(path, paint)
    }
}
```

## ⚠️ 第八步：注意事项与最佳实践

### 8.1 生命周期管理

```kotlin
class PPGActivity : AppCompatActivity() {

    override fun onDestroy() {
        super.onDestroy()

        // 断开设备连接
        BleServiceHelper.BleServiceHelper.disconnect(false)

        // 停止扫描
        BleServiceHelper.BleServiceHelper.stopScan()
    }

    override fun onPause() {
        super.onPause()

        // 暂停时停止扫描以节省电量
        BleServiceHelper.BleServiceHelper.stopScan()
    }
}
```

### 8.2 错误处理

```kotlin
class ErrorHandler {

    companion object {
        fun handleBleError(error: Throwable) {
            when (error) {
                is SecurityException -> {
                    Log.e("BLE", "权限不足: ${error.message}")
                    // 重新申请权限
                }
                is IllegalStateException -> {
                    Log.e("BLE", "蓝牙状态异常: ${error.message}")
                    // 检查蓝牙状态
                }
                else -> {
                    Log.e("BLE", "未知错误: ${error.message}")
                }
            }
        }
    }
}
```

### 8.3 性能优化

1. **数据缓存**: 避免频繁的UI更新
2. **内存管理**: 及时清理不需要的数据点
3. **线程管理**: 在后台线程进行数据分析
4. **电量优化**: 适时停止扫描和连接

## 🔧 第九步：测试验证

### 9.1 单元测试示例

```kotlin
@Test
fun testPPGAnalysis() {
    // 创建测试数据
    val testData = listOf(
        PPGDataPoint(100.0, 1000000000L),
        PPGDataPoint(110.0, 1020000000L),
        PPGDataPoint(105.0, 1040000000L)
    )

    // 执行分析
    val result = PPGManager.analyzeECG(testData)

    // 验证结果
    assertNotNull(result)
    assertTrue(result!!.rrIntervals.isNotEmpty())
}
```

## 📚 第十步：常见问题解决

### 10.1 连接问题
- 检查权限是否完整授予
- 确认蓝牙已开启
- 验证设备是否在范围内

### 10.2 数据接收问题
- 确认LiveEventBus注册正确
- 检查设备型号匹配
- 验证接口设置是否正确

### 10.3 分析结果异常
- 检查数据点数量是否足够
- 验证时间戳是否正确
- 确认采样频率设置

## 🎯 总结

通过以上步骤，您可以成功集成Lepu设备到您的Android项目中。关键点包括：

1. **正确的依赖配置**
2. **完整的权限申请**
3. **标准的初始化流程**
4. **合适的数据处理**
5. **良好的错误处理**

建议在集成过程中逐步测试每个功能模块，确保每一步都正常工作后再进行下一步。
```
