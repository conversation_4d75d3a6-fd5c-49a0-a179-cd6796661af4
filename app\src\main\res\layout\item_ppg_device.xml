<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:background="@android:color/transparent">

    <TextView
        android:id="@+id/tv_device_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/color_333333"
        android:textSize="14sp"
        tools:text="12346"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <TextView
        android:id="@+id/tv_start_collection"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_start_collection"
        android:textColor="@color/color_333333"
        android:textSize="14sp"
        android:background="@drawable/common_black_30_stroke_5_bg"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:paddingTop="2dp"
        android:paddingBottom="2dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@color/color_CDCDCD" />

</androidx.constraintlayout.widget.ConstraintLayout>

