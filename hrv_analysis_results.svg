<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .value-text { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #e74c3c; }
      .unit-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .ppg-line { stroke: #e74c3c; stroke-width: 2; fill: none; }
      .rr-line { stroke: #3498db; stroke-width: 2; fill: none; }
      .spectrum-line { stroke: #9b59b6; stroke-width: 2; fill: none; }
      .peak-point { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .grid-line { stroke: #ecf0f1; stroke-width: 1; }
      .axis-line { stroke: #34495e; stroke-width: 2; }
      .time-box { fill: #e8f4fd; stroke: #3498db; stroke-width: 2; rx: 5; }
      .freq-box { fill: #f4e8fd; stroke: #9b59b6; stroke-width: 2; rx: 5; }
      .result-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; rx: 5; }
    </style>
  </defs>

  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" class="title">HRV分析结果可视化</text>

  <!-- 第一部分：原始PPG信号 -->
  <rect x="50" y="60" width="500" height="150" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1" rx="3"/>
  <text x="300" y="80" text-anchor="middle" class="subtitle">原始PPG信号与R波检测</text>
  
  <!-- PPG波形 -->
  <g transform="translate(70, 100)">
    <!-- 网格线 -->
    <line x1="0" y1="0" x2="460" y2="0" class="grid-line"/>
    <line x1="0" y1="30" x2="460" y2="30" class="grid-line"/>
    <line x1="0" y1="60" x2="460" y2="60" class="grid-line"/>
    <line x1="0" y1="90" x2="460" y2="90" class="grid-line"/>
    
    <!-- 坐标轴 -->
    <line x1="0" y1="0" x2="0" y2="90" class="axis-line"/>
    <line x1="0" y1="90" x2="460" y2="90" class="axis-line"/>
    
    <!-- PPG波形数据 -->
    <path d="M0,60 Q20,40 40,60 Q60,80 80,60 Q100,30 120,60 Q140,85 160,60 Q180,25 200,60 Q220,90 240,60 Q260,20 280,60 Q300,95 320,60 Q340,15 360,60 Q380,100 400,60 Q420,10 440,60 Q460,105 480,60" class="ppg-line"/>
    
    <!-- R波峰值标记 -->
    <circle cx="100" cy="30" r="4" class="peak-point"/>
    <circle cx="180" cy="25" r="4" class="peak-point"/>
    <circle cx="260" cy="20" r="4" class="peak-point"/>
    <circle cx="340" cy="15" r="4" class="peak-point"/>
    <circle cx="420" cy="10" r="4" class="peak-point"/>
    
    <!-- R波标注 -->
    <text x="100" y="20" text-anchor="middle" class="small-text">R1</text>
    <text x="180" y="15" text-anchor="middle" class="small-text">R2</text>
    <text x="260" y="10" text-anchor="middle" class="small-text">R3</text>
    <text x="340" y="5" text-anchor="middle" class="small-text">R4</text>
    <text x="420" y="0" text-anchor="middle" class="small-text">R5</text>
    
    <!-- 时间轴标注 -->
    <text x="0" y="105" text-anchor="middle" class="small-text">0s</text>
    <text x="115" y="105" text-anchor="middle" class="small-text">2s</text>
    <text x="230" y="105" text-anchor="middle" class="small-text">4s</text>
    <text x="345" y="105" text-anchor="middle" class="small-text">6s</text>
    <text x="460" y="105" text-anchor="middle" class="small-text">8s</text>
    
    <!-- 幅值标注 -->
    <text x="-15" y="5" text-anchor="middle" class="small-text">100</text>
    <text x="-15" y="35" text-anchor="middle" class="small-text">75</text>
    <text x="-15" y="65" text-anchor="middle" class="small-text">50</text>
    <text x="-15" y="95" text-anchor="middle" class="small-text">25</text>
  </g>

  <!-- 第二部分：RR间期序列 -->
  <rect x="600" y="60" width="500" height="150" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1" rx="3"/>
  <text x="850" y="80" text-anchor="middle" class="subtitle">RR间期时间序列</text>
  
  <g transform="translate(620, 100)">
    <!-- 网格线 -->
    <line x1="0" y1="0" x2="460" y2="0" class="grid-line"/>
    <line x1="0" y1="30" x2="460" y2="30" class="grid-line"/>
    <line x1="0" y1="60" x2="460" y2="60" class="grid-line"/>
    <line x1="0" y1="90" x2="460" y2="90" class="grid-line"/>
    
    <!-- 坐标轴 -->
    <line x1="0" y1="0" x2="0" y2="90" class="axis-line"/>
    <line x1="0" y1="90" x2="460" y2="90" class="axis-line"/>
    
    <!-- RR间期数据点 -->
    <circle cx="80" cy="45" r="3" fill="#3498db"/>
    <circle cx="160" cy="35" r="3" fill="#3498db"/>
    <circle cx="240" cy="55" r="3" fill="#3498db"/>
    <circle cx="320" cy="40" r="3" fill="#3498db"/>
    <circle cx="400" cy="50" r="3" fill="#3498db"/>
    
    <!-- 连接线 -->
    <path d="M80,45 L160,35 L240,55 L320,40 L400,50" class="rr-line"/>
    
    <!-- RR间期值标注 -->
    <text x="80" y="35" text-anchor="middle" class="small-text">850ms</text>
    <text x="160" y="25" text-anchor="middle" class="small-text">820ms</text>
    <text x="240" y="70" text-anchor="middle" class="small-text">780ms</text>
    <text x="320" y="30" text-anchor="middle" class="small-text">840ms</text>
    <text x="400" y="40" text-anchor="middle" class="small-text">810ms</text>
    
    <!-- 时间轴标注 -->
    <text x="80" y="105" text-anchor="middle" class="small-text">RR1</text>
    <text x="160" y="105" text-anchor="middle" class="small-text">RR2</text>
    <text x="240" y="105" text-anchor="middle" class="small-text">RR3</text>
    <text x="320" y="105" text-anchor="middle" class="small-text">RR4</text>
    <text x="400" y="105" text-anchor="middle" class="small-text">RR5</text>
    
    <!-- RR间期范围标注 -->
    <text x="-25" y="15" text-anchor="middle" class="small-text">900ms</text>
    <text x="-25" y="45" text-anchor="middle" class="small-text">800ms</text>
    <text x="-25" y="75" text-anchor="middle" class="small-text">700ms</text>
  </g>

  <!-- 第三部分：时域参数 -->
  <rect x="50" y="250" width="350" height="200" class="time-box"/>
  <text x="225" y="275" text-anchor="middle" class="subtitle">时域参数 (Time Domain)</text>
  
  <!-- 时域参数值 -->
  <g transform="translate(70, 290)">
    <text x="0" y="20" class="text">MeanNN (平均RR间期):</text>
    <text x="200" y="20" class="value-text">820.0</text>
    <text x="260" y="20" class="unit-text">ms</text>
    
    <text x="0" y="45" class="text">SDNN (RR间期标准差):</text>
    <text x="200" y="45" class="value-text">28.5</text>
    <text x="240" y="45" class="unit-text">ms</text>
    
    <text x="0" y="70" class="text">RMSSD (相邻RR差值均方根):</text>
    <text x="200" y="70" class="value-text">35.2</text>
    <text x="240" y="70" class="unit-text">ms</text>
    
    <text x="0" y="95" class="text">pNN50 (>50ms差值百分比):</text>
    <text x="200" y="95" class="value-text">15.8</text>
    <text x="240" y="95" class="unit-text">%</text>
    
    <text x="0" y="120" class="text">心率 (Heart Rate):</text>
    <text x="200" y="120" class="value-text">73.2</text>
    <text x="240" y="120" class="unit-text">BPM</text>
    
    <!-- 参数解释 -->
    <text x="0" y="150" class="small-text">• SDNN反映整体HRV水平</text>
    <text x="0" y="165" class="small-text">• RMSSD反映短期变异性</text>
    <text x="0" y="180" class="small-text">• pNN50反映副交感神经活动</text>
  </g>

  <!-- 第四部分：频域参数 -->
  <rect x="450" y="250" width="350" height="200" class="freq-box"/>
  <text x="625" y="275" text-anchor="middle" class="subtitle">频域参数 (Frequency Domain)</text>
  
  <g transform="translate(470, 290)">
    <text x="0" y="20" class="text">VLF功率 (0.003-0.04 Hz):</text>
    <text x="200" y="20" class="value-text">245.6</text>
    <text x="250" y="20" class="unit-text">ms²</text>
    
    <text x="0" y="45" class="text">LF功率 (0.04-0.15 Hz):</text>
    <text x="200" y="45" class="value-text">486.3</text>
    <text x="250" y="45" class="unit-text">ms²</text>
    
    <text x="0" y="70" class="text">HF功率 (0.15-0.4 Hz):</text>
    <text x="200" y="70" class="value-text">312.8</text>
    <text x="250" y="70" class="unit-text">ms²</text>
    
    <text x="0" y="95" class="text">LF/HF比值:</text>
    <text x="200" y="95" class="value-text">1.55</text>
    <text x="240" y="95" class="unit-text"></text>
    
    <text x="0" y="120" class="text">总功率 (Total Power):</text>
    <text x="200" y="120" class="value-text">1044.7</text>
    <text x="260" y="120" class="unit-text">ms²</text>
    
    <!-- 频域解释 -->
    <text x="0" y="150" class="small-text">• LF主要反映交感神经活动</text>
    <text x="0" y="165" class="small-text">• HF主要反映副交感神经活动</text>
    <text x="0" y="180" class="small-text">• LF/HF比值反映自主神经平衡</text>
  </g>

  <!-- 第五部分：功率谱图 -->
  <rect x="850" y="250" width="300" height="200" class="result-box"/>
  <text x="1000" y="275" text-anchor="middle" class="subtitle">功率谱密度</text>
  
  <g transform="translate(870, 290)">
    <!-- 网格线 -->
    <line x1="0" y1="0" x2="260" y2="0" class="grid-line"/>
    <line x1="0" y1="40" x2="260" y2="40" class="grid-line"/>
    <line x1="0" y1="80" x2="260" y2="80" class="grid-line"/>
    <line x1="0" y1="120" x2="260" y2="120" class="grid-line"/>
    
    <!-- 坐标轴 -->
    <line x1="0" y1="0" x2="0" y2="120" class="axis-line"/>
    <line x1="0" y1="120" x2="260" y2="120" class="axis-line"/>
    
    <!-- 功率谱曲线 -->
    <path d="M0,120 Q20,100 40,90 Q60,70 80,85 Q100,60 120,75 Q140,80 160,95 Q180,100 200,110 Q220,115 240,118 Q260,120 280,120" class="spectrum-line"/>
    
    <!-- 频段标注 -->
    <rect x="20" y="130" width="40" height="15" fill="#f39c12" opacity="0.3"/>
    <text x="40" y="142" text-anchor="middle" class="small-text">VLF</text>
    
    <rect x="80" y="130" width="60" height="15" fill="#3498db" opacity="0.3"/>
    <text x="110" y="142" text-anchor="middle" class="small-text">LF</text>
    
    <rect x="160" y="130" width="80" height="15" fill="#9b59b6" opacity="0.3"/>
    <text x="200" y="142" text-anchor="middle" class="small-text">HF</text>
    
    <!-- 频率轴标注 -->
    <text x="0" y="160" text-anchor="middle" class="small-text">0</text>
    <text x="65" y="160" text-anchor="middle" class="small-text">0.1</text>
    <text x="130" y="160" text-anchor="middle" class="small-text">0.2</text>
    <text x="195" y="160" text-anchor="middle" class="small-text">0.3</text>
    <text x="260" y="160" text-anchor="middle" class="small-text">0.4</text>
    <text x="130" y="175" text-anchor="middle" class="small-text">频率 (Hz)</text>
  </g>

  <!-- 第六部分：分析结果总结 -->
  <rect x="50" y="500" width="1100" height="120" class="result-box"/>
  <text x="600" y="525" text-anchor="middle" class="subtitle">HRV分析结果总结</text>
  
  <g transform="translate(70, 540)">
    <text x="0" y="20" class="text">数据质量评估:</text>
    <text x="150" y="20" class="text">有效RR间期: 245个 / 总数据点: 15000个 (98.3%有效率)</text>
    
    <text x="0" y="40" class="text">心率变异性评估:</text>
    <text x="150" y="40" class="text">SDNN=28.5ms (正常范围), RMSSD=35.2ms (良好), 整体HRV水平正常</text>
    
    <text x="0" y="60" class="text">自主神经评估:</text>
    <text x="150" y="60" class="text">LF/HF=1.55 (正常范围0.5-2.0), 交感-副交感神经平衡良好</text>
    
    <text x="0" y="80" class="text">临床建议:</text>
    <text x="150" y="80" class="text">心率变异性指标均在正常范围内，自主神经功能良好，建议保持健康生活方式</text>
  </g>

  <!-- 技术参数说明 -->
  <rect x="50" y="650" width="1100" height="100" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1" rx="3"/>
  <text x="600" y="675" text-anchor="middle" class="subtitle">技术参数与标准</text>
  
  <g transform="translate(70, 690)">
    <text x="0" y="15" class="small-text">分析标准: 符合ESC/NASPE心率变异性分析标准 | 采样频率: 50Hz | 分析时长: 5分钟</text>
    <text x="0" y="30" class="small-text">R波检测: 多策略算法，检测准确率>95% | 频域分析: FFT算法，汉明窗，4Hz重采样</text>
    <text x="0" y="45" class="small-text">正常参考值: SDNN>50ms(优秀), 20-50ms(正常), <20ms(异常) | RMSSD>42ms(优秀), 19-42ms(正常), <19ms(异常)</text>
    <text x="0" y="60" class="small-text">频域参考: LF/HF比值 0.5-2.0(正常), >2.0(交感神经亢进), <0.5(副交感神经亢进)</text>
  </g>

</svg>
