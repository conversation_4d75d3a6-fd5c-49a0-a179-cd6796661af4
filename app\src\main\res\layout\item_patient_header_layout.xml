<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="45dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_root">

    <TextView
        android:id="@+id/tv_emr_id"
        android:layout_width="170dp"
        android:layout_height="match_parent"
        tools:text="@string/str_emr_id"
        android:textColor="@color/color_333333"
        android:textSize="14sp"
        android:maxLines="1"
        android:ellipsize="end"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        android:gravity="center"
        android:background="@drawable/item_patient_header_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_name"
        app:layout_constraintHorizontal_chainStyle="packed"/>

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="120dp"
        android:layout_height="match_parent"
        tools:text="@string/str_name"
        android:textColor="@color/color_333333"
        android:textSize="14sp"
        android:maxLines="1"
        android:ellipsize="end"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        android:gravity="center"
        android:background="@drawable/item_patient_header_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/tv_emr_id"
        app:layout_constraintRight_toLeftOf="@+id/tv_gender"/>

    <TextView
        android:id="@+id/tv_gender"
        android:layout_width="60dp"
        android:layout_height="match_parent"
        tools:text="@string/str_gender"
        android:textColor="@color/color_333333"
        android:textSize="14sp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center"
        android:background="@drawable/item_patient_header_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/tv_name"
        app:layout_constraintRight_toLeftOf="@+id/tv_age"/>

    <TextView
        android:id="@+id/tv_age"
        android:layout_width="60dp"
        android:layout_height="match_parent"
        tools:text="@string/str_age"
        android:textColor="@color/color_333333"
        android:textSize="14sp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center"
        android:background="@drawable/item_patient_header_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/tv_gender"
        app:layout_constraintRight_toLeftOf="@+id/tv_phone"/>

    <TextView
        android:id="@+id/tv_phone"
        android:layout_width="120dp"
        android:layout_height="match_parent"
        tools:text="@string/str_phone"
        android:textColor="@color/color_333333"
        android:textSize="14sp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center"
        android:background="@drawable/item_patient_header_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/tv_age"
        app:layout_constraintRight_toLeftOf="@+id/tv_creation_date"/>

    <TextView
        android:id="@+id/tv_creation_date"
        android:layout_width="120dp"
        android:layout_height="match_parent"
        tools:text="@string/str_creation_date"
        android:textColor="@color/color_333333"
        android:textSize="14sp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center"
        android:background="@drawable/item_patient_header_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/tv_phone"
        app:layout_constraintRight_toLeftOf="@+id/tv_revision_date"/>

    <TextView
        android:id="@+id/tv_revision_date"
        android:layout_width="120dp"
        android:layout_height="match_parent"
        tools:text="@string/str_revision_date"
        android:textColor="@color/color_333333"
        android:textSize="14sp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center"
        android:background="@drawable/item_patient_header_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/tv_creation_date"
        app:layout_constraintRight_toLeftOf="@+id/tv_operate"/>

    <TextView
        android:id="@+id/tv_operate"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        tools:text="@string/str_operate"
        android:textColor="@color/color_333333"
        android:textSize="14sp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center"
        android:background="@drawable/item_patient_header_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/tv_revision_date"
        app:layout_constraintRight_toRightOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>