package com.airdoc.ytrts.home.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: Patient
 * Author by lilin,Date on 2025/6/17 10:45
 * PS: Not easy to write code, please indicate.
 * 患者
 */
@Parcelize
data class Patient(
    //患者ID
    var id: Long? = null,
    //患者姓名
    var name:String? = null,
    //患者性别{1=男, 2=女},可用值:1,2
    var gender:Int? = null,
    //出生日期
    var birthday:String? = null,
    //年龄
    var age:Int? = null,
    //手机号
    var phoneNumber:String? = null,
    //创建日期
    var createTime:String? = null,
    //修改日期
    var updateTime:String? = null,
    //机构ID
    var organizationId: Long? = null,
) : Parcelable
