package com.airdoc.ytrts.home.api

import com.airdoc.component.common.net.entity.ApiResponse
import com.airdoc.ytrts.home.bean.Patient
import com.airdoc.ytrts.home.bean.PatientAdd
import com.airdoc.ytrts.home.bean.PatientList
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.HTTP
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query

/**
 * FileName: PatientApiService
 * Author by lilin,Date on 2025/6/20 10:31
 * PS: Not easy to write code, please indicate.
 */
interface PatientApiService {

    /**
     * 获取患者列表
     * @param page 页码,从1开始
     * @param size 每页条数,示例值(10)
     * @param sort 排序条件,示例值(按创建时间倒序：createTime,desc)
     * @param gender 性别{0=未知, 1=男, 2=女},可用值:0,1,2,示例值(1)
     * @param keywords 搜索关键词,示例值(张三)
     */
    @GET("re-mpd/api/patient")
    suspend fun getPatientList(
        @Query("page") page:Int,
        @Query("size") size:Int,
        @Query("sort") sort:String?,
        @Query("gender") gender:Int?,
        @Query("keywords") keywords:String?,
        @Header("Authorization") authorization: String
    ): ApiResponse<PatientList>

    /**
     * 添加患者
     */
    @POST("re-mpd/api/patient")
    suspend fun addPatient(
        @Header("Authorization") authorization: String,
        @Body patientReq: RequestBody
    ): ApiResponse<PatientAdd>

    /**
     * 修改患者
     * @param id 患者id
     */
    @PUT("re-mpd/api/patient/{id}")
    suspend fun modifyPatient(
        @Path("id") id: Long,
        @Header("Authorization") authorization: String,
        @Body patientReq: RequestBody
    ): ApiResponse<Any>

    /**
     * 查询患者信息
     * @param id 患者id
     */
    @GET("re-mpd/api/patient/{id}")
    suspend fun queryPatient(
        @Path("id") id:Long,
        @Header("Authorization") authorization: String
    ): ApiResponse<Patient>

    /**
     * 删除患者
     */
//    @DELETE("re-mpd/api/patient")
    @HTTP(method = "DELETE", path = "re-mpd/api/patient", hasBody = true)
    suspend fun deletePatient(
        @Body patientReq: RequestBody,
        @Header("Authorization") authorization: String
    ): ApiResponse<Any>

}