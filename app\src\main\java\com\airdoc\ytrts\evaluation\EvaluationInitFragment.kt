package com.airdoc.ytrts.evaluation

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.airdoc.component.common.base.BaseBindingFragment
import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.airdoc.ytrts.databinding.FragmentEvaluationInitBinding
import com.airdoc.ytrts.ppg.vm.PpgViewModel
import com.airdoc.ytrts.home.vm.EvaluationViewModel
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lepu.blepro.event.EventMsgConst
import com.lepu.blepro.objs.Bluetooth
import com.lepu.blepro.objs.BluetoothController

/**
 * FileName: EvaluationInitFragment
 * Author by lilin,Date on 2025/6/18 9:58
 * PS: Not easy to write code, please indicate.
 */
class EvaluationInitFragment : BaseBindingFragment<FragmentEvaluationInitBinding>(){

    companion object{
        private val TAG = EvaluationInitFragment::class.java.simpleName

        const val FRAGMENT_TAG = "EVALUATION_INIT"

        fun newInstance(): EvaluationInitFragment {
            val fragment = EvaluationInitFragment()
            return fragment
        }
    }

    private val evaluationVM by activityViewModels<EvaluationViewModel>()
    private val ppgVM by activityViewModels<PpgViewModel>()

    private var ppgDeviceAdapter = PpgDeviceAdapter()

    override fun createBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentEvaluationInitBinding {
        return FragmentEvaluationInitBinding.inflate(inflater,container,false)
    }

    override fun initParam() {
    }

    override fun initView() {
        initListeners()
        binding.rvDevice.layoutManager = LinearLayoutManager(mActivity).apply {
            orientation = LinearLayoutManager.VERTICAL
        }
        binding.rvDevice.adapter = ppgDeviceAdapter
    }

    override fun initObserver() {
        LiveEventBus.get<Boolean>(EventMsgConst.Ble.EventServiceConnectedAndInterfaceInit).observeSticky(this) {
            // BleService init success
            Logger.d(TAG, msg = "EventServiceConnectedAndInterfaceInit")
            (mActivity as? EvaluationActivity)?.startScan()
        }
        LiveEventBus.get<Bluetooth>(EventMsgConst.Discovery.EventDeviceFound).observe(this) {
            // scan result
            Logger.d(TAG, msg = "EventDeviceFound")
            splitDevices()
            //之前连接的设备
//            val lastPpgDevice = MMKVManager.decodeString(EvaluationPreference.PPG_DEVICE)
//            if (lastPpgDevice == "${it.name} ${it.macAddr}"){
//                ppgVM.setPpgDevice(it)
//                (mActivity as? EvaluationActivity)?.connectPpgDevice(it)
//            }
        }
    }

    override fun initData() {
    }

    private fun initListeners() {
        ppgDeviceAdapter.onItemClick = { bluetooth ->
            ppgVM.setPpgDevice(bluetooth)
            (mActivity as? EvaluationActivity)?.connectPpgDevice(bluetooth)
//            MMKVManager.encodeString(EvaluationPreference.PPG_DEVICE, "${bluetooth.name} ${bluetooth.macAddr}")
//            splitDevices()
        }
        binding.tvScan.setOnSingleClickListener {
            (mActivity as? EvaluationActivity)?.startScan()
        }
    }

    private fun splitDevices() {
        val bluetoothList = BluetoothController.getDevices()
        ppgDeviceAdapter.submitList(bluetoothList)
        binding.tvNoDevicesAvailable.isVisible = bluetoothList.isEmpty()
    }

}