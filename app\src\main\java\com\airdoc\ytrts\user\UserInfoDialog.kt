package com.airdoc.ytrts.user

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.graphics.Rect
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.View.OnTouchListener
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.core.graphics.drawable.toDrawable
import com.airdoc.component.common.base.BaseCommonDialog
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.airdoc.ytrts.R
import com.airdoc.ytrts.databinding.DialogUserInfoBinding
import com.airdoc.ytrts.user.bean.User
import com.airdoc.ytrts.user.enumeration.Gender
import com.google.i18n.phonenumbers.PhoneNumberUtil
import java.util.Locale

/**
 * FileName: UserInfoDialog
 * Author by lilin,Date on 2025/6/19 10:54
 * PS: Not easy to write code, please indicate.
 */
class UserInfoDialog(context: Context) : BaseCommonDialog(context) {

    companion object{
        private val TAG = UserInfoDialog::class.java.simpleName
    }
    private lateinit var binding: DialogUserInfoBinding
    var onOkClick:((user: User) -> Unit)? = null
    private val phoneUtil = PhoneNumberUtil.getInstance()
    private var user = UserManager.getUserInfo()?: User()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //设置Dialog背景透明
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        binding = DialogUserInfoBinding.inflate(LayoutInflater.from(context))
        setContentView(binding.root)
        window?.apply {
            val width = 450.dp2px(context)
            val height = 395.dp2px(context)
            setLayout(width, height)
        }
        setCancelable(false)
        setCanceledOnTouchOutside(false)

        initView()
        initListener()
    }

    private fun initView() {
        binding.etName.setText(user.name.orEmpty())
        when(user.gender){
            Gender.FEMALE.num ->{
                binding.rgGender.check(R.id.rb_female)
                binding.ivAvatar.setImageResource(R.drawable.ic_female_avatar_round)
            }
            else ->{
                binding.rgGender.check(R.id.rb_male)
                binding.ivAvatar.setImageResource(R.drawable.ic_male_avatar_round)
            }
        }
        binding.etPhone.setText(user.phoneNumber.orEmpty())
    }

    private fun initListener() {
        binding.tvOk.setOnSingleClickListener {
            if (checkUser(user)){
                onOkClick?.invoke(user)
                dismiss()
            }
        }
        binding.tvCancel.setOnSingleClickListener {
            dismiss()
        }
        binding.etName.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                user.name = s.toString().trim()
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
        binding.etPhone.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val phoneText = s.toString().replace("\\s+".toRegex(), "")
                // 只允许数字输入
                val digitsOnly = phoneText.replace("[^0-9]".toRegex(), "")
                // 限制手机号长度≤14位
                if (digitsOnly.length <= 14) {
                    user.phoneNumber = digitsOnly
                    // 如果输入的内容与过滤后的内容不同，更新输入框
                    if (phoneText != digitsOnly) {
                        binding.etPhone.setText(digitsOnly)
                        binding.etPhone.setSelection(digitsOnly.length)
                    }
                } else {
                    // 如果超过14位，截取前14位并更新输入框
                    val truncatedPhone = digitsOnly.substring(0, 14)
                    user.phoneNumber = truncatedPhone
                    binding.etPhone.setText(truncatedPhone)
                    binding.etPhone.setSelection(truncatedPhone.length)
                }
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
        binding.rgGender.setOnCheckedChangeListener { _, checkedId ->
            when(checkedId){
                R.id.rb_male ->{
                    user.gender = Gender.MALE.num
                    binding.ivAvatar.setImageResource(R.drawable.ic_male_avatar_round)
                }
                R.id.rb_female ->{
                    user.gender = Gender.FEMALE.num
                    binding.ivAvatar.setImageResource(R.drawable.ic_female_avatar_round)
                }
            }
        }
        // 监听窗口触摸事件
        window?.decorView?.setOnTouchListener(object :OnTouchListener{
            @SuppressLint("ClickableViewAccessibility")
            override fun onTouch(v: View?, event: MotionEvent?): Boolean {
                if (event?.action == MotionEvent.ACTION_UP) {
                    isOutsideDialog(event).also {
                        Logger.d(TAG, msg = "isOutsideDialog = $it")
                        if (it) {
                            handleOutsideClick()
                            return true
                        } else {
                            return false
                        }
                    }
                }
                return false
            }

        })
    }

    private fun checkUser(user: User):Boolean{
        // 验证姓名
        val name = user.name?.trim()
        if (name.isNullOrBlank()){
            Toast.makeText(context,context.getString(R.string.str_invalid_name_input),Toast.LENGTH_SHORT).show()
            return false
        }
        // 验证姓名长度不超过32位
        if (name.length > 32) {
            Toast.makeText(context,context.getString(R.string.str_invalid_name_length),Toast.LENGTH_SHORT).show()
            return false
        }
        // 验证姓名只能包含中文或英文字符
        val nameRegex = "^[a-zA-Z\\u4e00-\\u9fa5\\s]+$".toRegex()
        if (!nameRegex.matches(name)) {
            Toast.makeText(context,context.getString(R.string.str_invalid_name_format),Toast.LENGTH_SHORT).show()
            return false
        }

        // 验证手机号（必填项）
        val phone = user.phoneNumber?.trim()
        if (phone.isNullOrBlank()){
            Toast.makeText(context,"手机号不能为空",Toast.LENGTH_SHORT).show()
            return false
        }
        // 验证手机号长度≤14位
        if (phone.length > 14) {
            Toast.makeText(context,context.getString(R.string.str_invalid_phone_number_length),Toast.LENGTH_SHORT).show()
            return false
        }
        // 验证手机号只能包含数字
        if (!phone.matches("^[0-9]+$".toRegex())) {
            Toast.makeText(context,"手机号只能包含数字",Toast.LENGTH_SHORT).show()
            return false
        }
        // 暂时注释掉手机号格式验证，只保留长度限制
        /*
        try {
            val phoneNumber = phoneUtil.parse(phone, Locale.SIMPLIFIED_CHINESE.country)
            val validNumber = phoneUtil.isValidNumber(phoneNumber)
            Logger.d(TAG, msg = "checkUser phoneNumber = $phoneNumber, validNumber = $validNumber")
            if (!validNumber){
                Toast.makeText(context,context.getString(R.string.str_invalid_phone_number_format),Toast.LENGTH_SHORT).show()
                return false
            }
        }catch (e:Exception){
            Toast.makeText(context,context.getString(R.string.str_invalid_phone_number_format),Toast.LENGTH_SHORT).show()
            return false
        }
        */
        return true
    }

    /**
     * 点击了外部区域
     */
    private fun handleOutsideClick() {
        val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        val currentFocus = window?.decorView?.findFocus() ?: window?.currentFocus
        if (currentFocus != null){
            currentFocus.clearFocus()
            imm.hideSoftInputFromWindow(currentFocus.windowToken, 0)
        }
    }

    private fun isOutsideDialog(event: MotionEvent): Boolean {
        // 获取 clRoot 在屏幕中的绝对位置
        val location = IntArray(2)
        binding.clRoot.getLocationOnScreen(location)
        val left = location[0]
        val top = location[1]
        val right = left + binding.clRoot.width
        val bottom = top + binding.clRoot.height

        // 构建修正后的 Rect
        val adjustedRect = Rect(left, top, right, bottom)
        return !adjustedRect.contains(event.rawX.toInt(), event.rawY.toInt())
    }

}
