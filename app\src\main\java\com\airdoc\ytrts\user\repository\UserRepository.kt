package com.airdoc.ytrts.user.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.airdoc.ytrts.net.MainRetrofitClient
import com.airdoc.ytrts.user.api.UserApiService
import com.airdoc.ytrts.user.bean.LoginInfo
import com.airdoc.ytrts.user.bean.User
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * FileName: UserRepository
 * Author by lilin,Date on 2025/6/19 17:49
 * PS: Not easy to write code, please indicate.
 */
class UserRepository : BaseRepository() {

    /**
     * 查询患者信息
     * @param authorization 授权token,示例值(Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQ)
     */
    suspend fun getUserInfo(authorization: String): ApiResponse<User> {
        return executeHttp {
            MainRetrofitClient.createService(UserApiService::class.java).getUserInfo(authorization)
        }
    }

    /**
     * 登录
     * @param username 用户名
     * @param password 密码(加密)
     * @param authType 登录方式
     */
    suspend fun login(username: String, password: String,authType: String = "ACCOUNT"): ApiResponse<LoginInfo> {
        return executeHttp {
            val hashMap = HashMap<String, Any>()
            hashMap["username"] = username
            hashMap["password"] = password
            hashMap["authType"] = authType
            val json = gson.toJson(hashMap)
            val requestBody =
                json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
            MainRetrofitClient.createService(UserApiService::class.java).login(requestBody)
        }
    }

    /**
     * 登出
     * @param authorization 授权token,示例值(Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQ)
     */
    suspend fun logout(authorization: String): ApiResponse<Any> {
        return executeHttp {
            MainRetrofitClient.createService(UserApiService::class.java).logout(authorization)
        }
    }

    /**
     * 修改用户信息
     * @param name 姓名
     * @param gender 性别
     * @param phoneNumber 手机号
     */
    suspend fun updateUserInfo(name: String, gender: Int,phoneNumber: String,authorization: String): ApiResponse<Any> {
        return executeHttp {
            val hashMap = HashMap<String, Any>()
            hashMap["name"] = name
            hashMap["gender"] = gender
            hashMap["phoneNumber"] = phoneNumber
            val json = gson.toJson(hashMap)
            val requestBody =
                json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
            MainRetrofitClient.createService(UserApiService::class.java).updateUserInfo(authorization,requestBody)
        }
    }

    /**
     * 修改用户信息
     * @param oldPassword 旧密码(加密)
     * @param newPassword 新密码(加密)
     */
    suspend fun changePassword(oldPassword: String, newPassword: String,authorization: String): ApiResponse<Any> {
        return executeHttp {
            val hashMap = HashMap<String, Any>()
            hashMap["oldPassword"] = oldPassword
            hashMap["newPassword"] = newPassword
            val json = gson.toJson(hashMap)
            val requestBody =
                json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
            MainRetrofitClient.createService(UserApiService::class.java).changePassword(authorization,requestBody)
        }
    }

}