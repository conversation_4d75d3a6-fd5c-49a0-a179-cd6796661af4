<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="#EFF3F6">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/icon_back_black_coarse"
        android:layout_marginTop="20dp"
        android:layout_marginStart="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_new_evaluation"
        android:textColor="@color/color_333333"
        android:textSize="18sp"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <androidx.cardview.widget.CardView
        android:id="@+id/card_info"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="20dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        style="@style/common_card"
        app:layout_constraintTop_toBottomOf="@+id/iv_back">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tv_id"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_333333"
                android:textSize="14sp"
                tools:text="ID：12345678"
                android:layout_marginStart="20dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/tv_name"
                app:layout_constraintHorizontal_chainStyle="spread_inside"/>

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_333333"
                android:textSize="14sp"
                tools:text="姓名：张三"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/tv_id"
                app:layout_constraintRight_toLeftOf="@+id/tv_age"/>

            <TextView
                android:id="@+id/tv_age"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_333333"
                android:textSize="14sp"
                tools:text="年龄：28"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/tv_name"
                app:layout_constraintRight_toLeftOf="@+id/tv_evaluation_date"/>

            <TextView
                android:id="@+id/tv_evaluation_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_333333"
                android:textSize="14sp"
                tools:text="评估日期：2025-05-12 12:34:45"
                android:layout_marginEnd="20dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/tv_age"
                app:layout_constraintRight_toRightOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/card_evaluation"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="20dp"
        android:layout_marginStart="20dp"
        android:layout_marginBottom="20dp"
        android:layout_marginEnd="20dp"
        style="@style/common_card"
        app:layout_constraintTop_toBottomOf="@+id/card_info"
        app:layout_constraintBottom_toBottomOf="parent">

        <FrameLayout
            android:id="@+id/fl_evaluation_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>